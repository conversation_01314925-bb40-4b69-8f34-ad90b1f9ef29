* [Localization Package](index.md)
* [What's new](whats-new.md)
* Guides
  * [Upgrade guide](upgrade-guide.md)
  * [Installation](Installation.md)
  * [Package samples](Package-Samples.md)
  * [Quick start guide: 2019.4+](QuickStartGuide.md)
  * [Quick start guide: 2020.3+](QuickStartGuideWithVariants.md)
* Reference
  * [Addressables Integration](Addressables.md)
  * [Component Localizers](ComponentLocalizers.md)
  * Extensions
    * [CSV](CSV.md)
    * [Google Sheets](Google-Sheets.md)
      * [Sheets Service Provider](Google-Sheets-Sheets-Service-Provider.md)
      * [Configuring Authentication](Google-Sheets-Configuring-Authentication.md)
      * [Syncing String Table Collections](Google-Sheets-Syncing-StringTableCollections.md)
    * [XLIFF](XLIFF.md)
  * [Locale](Locale.md)
  * [Localization Settings](LocalizationSettings.md)
    * [Locale Selector](LocaleSelector.md)
  * [Localized Property Variants](LocalizedPropertyVariants.md)
  * [Metadata](Metadata.md)
    * [Platform Overrides](Metadata-Platform-Overrides.md)
  * Platforms
    * [Android](Android-App-Localization.md)
    * [Editor](EditModeSupport.md)
    * [Apple (iOS, macOS, tvOS, visionOS)](iOS-App-Localization.md)
  * [Pseudo-Localization](Pseudo-Localization.md)
    * [Methods](Pseudo-Localization-Methods.md)
  * [Scripting](Scripting.md)
  * [Search Filters](SearchFilters.md)
  * [Smart Strings](Smart/SmartStrings.md)
    * Sources
      * [Default](Smart/Default-Source.md)
      * [Dictionary](Smart/Dictionary-Source.md)
      * [Persistent Variables](Smart/Persistent-Variables-Source.md)
      * [List](Smart/List-Source.md)
      * [Reflection](Smart/Reflection-Source.md)
      * [Value Tuple](Smart/Value-Tuple-Source.md)
      * [XML](Smart/Xml-Source.md)
      * [Creating a custom source](Smart/Creating-a-Custom-Source.md)
    * Formatters
      * [Choose](Smart/Choose-Formatter.md)
      * [Conditional](Smart/Conditional-Formatter.md)
      * [Default](Smart/Default-Formatter.md)
      * [Is Match](Smart/IsMatch-Formatter.md)
      * [List](Smart/List-Formatter.md)
      * [Plural](Smart/Plural-Formatter.md)
      * [Sub String](Smart/SubString-Formatter.md)
      * [Template](Smart/Template-Formatter.md)
      * [Time](Smart/Time-Formatter.md)
      * [X Element](Smart/XElement-Formatter.md)
      * [Creating a custom formatter](Smart/Creating-a-Custom-Formatter.md)
  * Tables
    * [Localization Tables Window](LocalizationTablesWindow.md)
    * [String Tables](StringTables.md)
    * [Asset Tables](AssetTables.md)
    * [Table Keys](TableEntryKeys.md)
  * [UI Toolkit](UIToolkit.md)
