# Package samples

The Localization package comes with a set of samples to help you get started.

A sample is a set of assets that you can import into your Unity project and use as a base to build upon or learn how to use a feature. A package sample can contain anything from a single C# script to multiple scenes.

## Importing package samples

To import package samples, use the Unity Package Manager window:

1. Go to Window > **Package Manager** and, in the packages list view, select **Localization**.
2. In the package [details view](https://docs.unity3d.com/Manual/upm-ui-details.html), find the **Samples** section.
3. Find the sample you want to import and click the Import button next to it.
Unity imports Localization package samples into `Assets/Samples/Localization/<package version>/<sample name>`.
