<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: ProcessStringEntry Pages: 1 -->
<svg width="402pt" height="611pt"
 viewBox="0.00 0.00 402.00 611.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="page0,1_graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 607)">
<title>ProcessStringEntry</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-607 398,-607 398,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_process</title>
<path fill="#daedfd" stroke="#2196f3" d="M20,-65C20,-65 374,-65 374,-65 380,-65 386,-71 386,-77 386,-77 386,-528 386,-528 386,-534 380,-540 374,-540 374,-540 20,-540 20,-540 14,-540 8,-534 8,-528 8,-528 8,-77 8,-77 8,-71 14,-65 20,-65"/>
<text text-anchor="middle" x="314.5" y="-528" font-family="inter" font-size="10.00">Process String Table Entry</text>
</g>
<!-- getString -->
<g id="node1" class="node">
<title>getString</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M171,-603C171,-603 27,-603 27,-603 21,-603 15,-597 15,-591 15,-591 15,-579 15,-579 15,-573 21,-567 27,-567 27,-567 171,-567 171,-567 177,-567 183,-573 183,-579 183,-579 183,-591 183,-591 183,-597 177,-603 171,-603"/>
<text text-anchor="middle" x="99" y="-582.5" font-family="inter" font-size="10.00">Get Localized String From Entry</text>
</g>
<!-- isSmart -->
<g id="node2" class="node">
<title>isSmart</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M87.71,-497.92C87.71,-497.92 27.29,-476.08 27.29,-476.08 21.64,-474.04 21.64,-469.96 27.29,-467.92 27.29,-467.92 87.71,-446.08 87.71,-446.08 93.36,-444.04 104.64,-444.04 110.29,-446.08 110.29,-446.08 170.71,-467.92 170.71,-467.92 176.36,-469.96 176.36,-474.04 170.71,-476.08 170.71,-476.08 110.29,-497.92 110.29,-497.92 104.64,-499.96 93.36,-499.96 87.71,-497.92"/>
<text text-anchor="middle" x="99" y="-475" font-family="inter" font-size="10.00">Is this a</text>
<text text-anchor="middle" x="99" y="-464" font-family="inter" font-size="10.00">Smart String?</text>
</g>
<!-- getString&#45;&gt;isSmart -->
<g id="edge1" class="edge">
<title>getString&#45;&gt;isSmart</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M99,-566.66C99,-552.22 99,-531.09 99,-512.56"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="102.5,-512.31 99,-502.31 95.5,-512.31 102.5,-512.31"/>
</g>
<!-- smartFormat -->
<g id="node3" class="node">
<title>smartFormat</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M143,-384C143,-384 55,-384 55,-384 49,-384 43,-378 43,-372 43,-372 43,-360 43,-360 43,-354 49,-348 55,-348 55,-348 143,-348 143,-348 149,-348 155,-354 155,-360 155,-360 155,-372 155,-372 155,-378 149,-384 143,-384"/>
<text text-anchor="middle" x="99" y="-363.5" font-family="inter" font-size="10.00">Apply Smart Format</text>
</g>
<!-- isSmart&#45;&gt;smartFormat -->
<g id="edge3" class="edge">
<title>isSmart&#45;&gt;smartFormat</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M99,-441.75C99,-426.91 99,-408.99 99,-394.42"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="102.5,-394.03 99,-384.03 95.5,-394.03 102.5,-394.03"/>
<text text-anchor="middle" x="108" y="-405" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- includesArguments -->
<g id="node5" class="node">
<title>includesArguments</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M287.32,-507.53C287.32,-507.53 228.68,-477.47 228.68,-477.47 223.34,-474.74 223.34,-469.26 228.68,-466.53 228.68,-466.53 287.32,-436.47 287.32,-436.47 292.66,-433.74 303.34,-433.74 308.68,-436.47 308.68,-436.47 367.32,-466.53 367.32,-466.53 372.66,-469.26 372.66,-474.74 367.32,-477.47 367.32,-477.47 308.68,-507.53 308.68,-507.53 303.34,-510.26 292.66,-510.26 287.32,-507.53"/>
<text text-anchor="middle" x="298" y="-480.5" font-family="inter" font-size="10.00">Were format </text>
<text text-anchor="middle" x="298" y="-469.5" font-family="inter" font-size="10.00">arguments</text>
<text text-anchor="middle" x="298" y="-458.5" font-family="inter" font-size="10.00"> provided?</text>
</g>
<!-- isSmart&#45;&gt;includesArguments -->
<g id="edge4" class="edge">
<title>isSmart&#45;&gt;includesArguments</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M182.24,-472C190.65,-472 199.21,-472 207.68,-472"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="207.96,-475.5 217.96,-472 207.96,-468.5 207.96,-475.5"/>
<text text-anchor="middle" x="200" y="-478" font-family="inter" font-size="10.00">No</text>
</g>
<!-- pseudoLocale -->
<g id="node7" class="node">
<title>pseudoLocale</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M199.15,-305.88C199.15,-305.88 133.85,-275.12 133.85,-275.12 128.43,-272.56 128.43,-267.44 133.85,-264.88 133.85,-264.88 199.15,-234.12 199.15,-234.12 204.57,-231.56 215.43,-231.56 220.85,-234.12 220.85,-234.12 286.15,-264.88 286.15,-264.88 291.57,-267.44 291.57,-272.56 286.15,-275.12 286.15,-275.12 220.85,-305.88 220.85,-305.88 215.43,-308.44 204.57,-308.44 199.15,-305.88"/>
<text text-anchor="middle" x="210" y="-278.5" font-family="inter" font-size="10.00">Is a</text>
<text text-anchor="middle" x="210" y="-267.5" font-family="inter" font-size="10.00">Pseudo&#45;Locale</text>
<text text-anchor="middle" x="210" y="-256.5" font-family="inter" font-size="10.00">being used?</text>
</g>
<!-- smartFormat&#45;&gt;pseudoLocale -->
<g id="edge7" class="edge">
<title>smartFormat&#45;&gt;pseudoLocale</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M119.1,-347.98C133.73,-335.59 153.99,-318.43 171.67,-303.46"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="174.21,-305.89 179.58,-296.76 169.69,-300.55 174.21,-305.89"/>
</g>
<!-- applyPseudo -->
<g id="node4" class="node">
<title>applyPseudo</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M162,-182C162,-182 46,-182 46,-182 40,-182 34,-176 34,-170 34,-170 34,-158 34,-158 34,-152 40,-146 46,-146 46,-146 162,-146 162,-146 168,-146 174,-152 174,-158 174,-158 174,-170 174,-170 174,-176 168,-182 162,-182"/>
<text text-anchor="middle" x="104" y="-161.5" font-family="inter" font-size="10.00">Apply Pseudo Localization</text>
</g>
<!-- returnTranslated -->
<g id="node8" class="node">
<title>returnTranslated</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M244,-109C244,-109 176,-109 176,-109 170,-109 164,-103 164,-97 164,-97 164,-85 164,-85 164,-79 170,-73 176,-73 176,-73 244,-73 244,-73 250,-73 256,-79 256,-85 256,-85 256,-97 256,-97 256,-103 250,-109 244,-109"/>
<text text-anchor="middle" x="210" y="-94" font-family="inter" font-size="10.00">Return localized</text>
<text text-anchor="middle" x="210" y="-83" font-family="inter" font-size="10.00">text</text>
</g>
<!-- applyPseudo&#45;&gt;returnTranslated -->
<g id="edge10" class="edge">
<title>applyPseudo&#45;&gt;returnTranslated</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M129.39,-145.99C143.32,-136.67 160.77,-124.97 175.87,-114.86"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="178.09,-117.59 184.45,-109.11 174.19,-111.77 178.09,-117.59"/>
</g>
<!-- stringFormat -->
<g id="node6" class="node">
<title>stringFormat</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M341.5,-384C341.5,-384 254.5,-384 254.5,-384 248.5,-384 242.5,-378 242.5,-372 242.5,-372 242.5,-360 242.5,-360 242.5,-354 248.5,-348 254.5,-348 254.5,-348 341.5,-348 341.5,-348 347.5,-348 353.5,-354 353.5,-360 353.5,-360 353.5,-372 353.5,-372 353.5,-378 347.5,-384 341.5,-384"/>
<text text-anchor="middle" x="298" y="-363.5" font-family="inter" font-size="10.00">Apply String.Format</text>
</g>
<!-- includesArguments&#45;&gt;stringFormat -->
<g id="edge5" class="edge">
<title>includesArguments&#45;&gt;stringFormat</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M298,-430.79C298,-418.49 298,-405.26 298,-394.05"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="301.5,-394.03 298,-384.03 294.5,-394.03 301.5,-394.03"/>
<text text-anchor="middle" x="307" y="-405" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- includesArguments&#45;&gt;pseudoLocale -->
<g id="edge6" class="edge">
<title>includesArguments&#45;&gt;pseudoLocale</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M267.55,-446.1C250.45,-430.25 230.5,-408.19 220,-384 211.38,-364.14 208.32,-340.5 207.63,-319.99"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="211.13,-319.84 207.47,-309.89 204.13,-319.95 211.13,-319.84"/>
<text text-anchor="middle" x="227" y="-363.5" font-family="inter" font-size="10.00">No</text>
</g>
<!-- stringFormat&#45;&gt;pseudoLocale -->
<g id="edge8" class="edge">
<title>stringFormat&#45;&gt;pseudoLocale</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M281.86,-347.76C271.06,-336.22 256.45,-320.62 243.23,-306.49"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="245.45,-303.74 236.06,-298.83 240.34,-308.53 245.45,-303.74"/>
</g>
<!-- pseudoLocale&#45;&gt;applyPseudo -->
<g id="edge9" class="edge">
<title>pseudoLocale&#45;&gt;applyPseudo</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M182.43,-241.95C165.87,-225.71 144.98,-205.21 128.93,-189.46"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="131.06,-186.65 121.47,-182.14 126.16,-191.65 131.06,-186.65"/>
<text text-anchor="middle" x="159" y="-203" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- pseudoLocale&#45;&gt;returnTranslated -->
<g id="edge11" class="edge">
<title>pseudoLocale&#45;&gt;returnTranslated</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M210,-228.84C210,-195.37 210,-148.61 210,-119.33"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="213.5,-119.03 210,-109.03 206.5,-119.03 213.5,-119.03"/>
<text text-anchor="middle" x="217" y="-161.5" font-family="inter" font-size="10.00">No</text>
</g>
<!-- localizedText -->
<g id="node9" class="node">
<title>localizedText</title>
<path fill="#e9f4e9" stroke="#67bc6b" d="M240,-36C240,-36 180,-36 180,-36 174,-36 168,-30 168,-24 168,-24 168,-12 168,-12 168,-6 174,0 180,0 180,0 240,0 240,0 246,0 252,-6 252,-12 252,-12 252,-24 252,-24 252,-30 246,-36 240,-36"/>
<text text-anchor="middle" x="210" y="-15.5" font-family="inter" font-size="10.00">Localized Text</text>
</g>
<!-- returnTranslated&#45;&gt;localizedText -->
<g id="edge2" class="edge">
<title>returnTranslated&#45;&gt;localizedText</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M210,-72.81C210,-64.79 210,-55.05 210,-46.07"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="213.5,-46.03 210,-36.03 206.5,-46.03 213.5,-46.03"/>
</g>
</g>
</svg>
