<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.48.0 (20210717.1556)
 -->
<!-- Title: XMLFormatter Pages: 1 -->
<svg width="289pt" height="113pt"
 viewBox="0.00 0.00 289.00 113.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 109)">
<title>XMLFormatter</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-109 285,-109 285,4 -4,4"/>
<!-- smartString -->
<g id="node1" class="node">
<title>smartString</title>
<text text-anchor="start" x="102.5" y="-15" font-family="inter" font-size="10.00">{</text>
<text text-anchor="start" x="108.5" y="-15" font-family="inter" font-size="10.00" fill="#f37021">value</text>
<text text-anchor="start" x="135.5" y="-15" font-family="inter" font-size="10.00">:</text>
<text text-anchor="start" x="140.5" y="-15" font-family="inter" font-size="10.00" fill="#67bc6b">xml</text>
<text text-anchor="start" x="158.5" y="-15" font-family="inter" font-size="10.00">()</text>
<text text-anchor="start" x="167.5" y="-15" font-family="inter" font-size="10.00">}</text>
</g>
<!-- selector -->
<g id="node2" class="node">
<title>selector</title>
<text text-anchor="middle" x="45.5" y="-86" font-family="inter" font-size="10.00" fill="#f37021">Any XElement value</text>
</g>
<!-- selector&#45;&gt;smartString -->
<g id="edge1" class="edge">
<title>selector&#45;&gt;smartString:selector1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M59.61,-82.98C78.87,-75.93 111.85,-60.45 119.07,-35.17"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="122.58,-35.39 120.5,-25 115.65,-34.42 122.58,-35.39"/>
</g>
<!-- formatterName -->
<g id="node3" class="node">
<title>formatterName</title>
<text text-anchor="middle" x="148.5" y="-97" font-family="inter" font-size="10.00" fill="#67bc6b">Formatter Name.</text>
<text text-anchor="middle" x="148.5" y="-86" font-family="inter" font-size="10.00" fill="#67bc6b">&quot;xelement&quot;, &quot;xml&quot;,</text>
<text text-anchor="middle" x="148.5" y="-75" font-family="inter" font-size="10.00" fill="#67bc6b">&quot;x&quot; or implcit</text>
</g>
<!-- formatterName&#45;&gt;smartString -->
<g id="edge2" class="edge">
<title>formatterName&#45;&gt;smartString:name</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M148.5,-71.83C148.5,-61.86 148.5,-48.4 148.5,-35.33"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="152,-35 148.5,-25 145,-35 152,-35"/>
</g>
<!-- formatterOptions -->
<g id="node4" class="node">
<title>formatterOptions</title>
<text text-anchor="middle" x="243.5" y="-91.5" font-family="inter" font-size="10.00" fill="#eb417a">No Format</text>
<text text-anchor="middle" x="243.5" y="-80.5" font-family="inter" font-size="10.00" fill="#eb417a">options required</text>
</g>
<!-- formatterOptions&#45;&gt;smartString -->
<g id="edge3" class="edge">
<title>formatterOptions&#45;&gt;smartString:option1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M211.95,-77.36C192.23,-69.11 169.52,-55.43 163.84,-34.91"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="167.31,-34.44 162.5,-25 160.37,-35.38 167.31,-34.44"/>
</g>
</g>
</svg>
