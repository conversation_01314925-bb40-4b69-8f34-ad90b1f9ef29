%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a07b5cd0b1b829245bc8c4b6978793e8, type: 3}
  m_Name: Localization Settings
  m_EditorClassIdentifier: 
  m_StartupSelectors:
  - rid: 5393902103725342968
  - rid: 5393902103725342969
  - rid: 5393902103725342970
  m_AvailableLocales:
    rid: 5393902103725342971
  m_AssetDatabase:
    rid: 5393902103725342972
  m_StringDatabase:
    rid: 5393902103725342973
  m_Metadata:
    m_Items: []
  m_ProjectLocaleIdentifier:
    m_Code: en
  m_PreloadBehavior: 1
  m_InitializeSynchronously: 0
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
    - rid: 5393902103725342968
      type: {class: CommandLineLocaleSelector, ns: UnityEngine.Localization.Settings, asm: Unity.Localization}
      data:
        m_CommandLineArgument: -language=
    - rid: 5393902103725342969
      type: {class: SystemLocaleSelector, ns: UnityEngine.Localization.Settings, asm: Unity.Localization}
      data: 
    - rid: 5393902103725342970
      type: {class: SpecificLocaleSelector, ns: UnityEngine.Localization.Settings, asm: Unity.Localization}
      data:
        m_LocaleId:
          m_Code: en
    - rid: 5393902103725342971
      type: {class: LocalesProvider, ns: UnityEngine.Localization.Settings, asm: Unity.Localization}
      data: 
    - rid: 5393902103725342972
      type: {class: LocalizedAssetDatabase, ns: UnityEngine.Localization.Settings, asm: Unity.Localization}
      data:
        m_DefaultTableReference:
          m_TableCollectionName: 
        m_CustomTableProvider:
          rid: -2
        m_CustomTablePostprocessor:
          rid: -2
        m_AsynchronousBehaviour: 0
        m_UseFallback: 0
    - rid: 5393902103725342973
      type: {class: LocalizedStringDatabase, ns: UnityEngine.Localization.Settings, asm: Unity.Localization}
      data:
        m_DefaultTableReference:
          m_TableCollectionName: 
        m_CustomTableProvider:
          rid: -2
        m_CustomTablePostprocessor:
          rid: -2
        m_AsynchronousBehaviour: 0
        m_UseFallback: 0
        m_MissingTranslationState: 1
        m_NoTranslationFoundMessage: No translation found for '{key}' in {table.TableCollectionName}
        m_SmartFormat:
          rid: 5393902103725342974
    - rid: 5393902103725342974
      type: {class: SmartFormatter, ns: UnityEngine.Localization.SmartFormat, asm: Unity.Localization}
      data:
        m_Settings:
          rid: 5393902103725342975
        m_Parser:
          rid: 5393902103725342976
        m_Sources:
        - rid: 5393902103725342977
        - rid: 5393902103725342978
        - rid: 5393902103725342979
        - rid: 5393902103725342980
        - rid: 5393902103725342981
        - rid: 5393902103725342982
        - rid: 5393902103725342983
        m_Formatters:
        - rid: 5393902103725342977
        - rid: 5393902103725342984
        - rid: 5393902103725342985
        - rid: 5393902103725342986
        - rid: 5393902103725342987
        - rid: 5393902103725342988
        - rid: 5393902103725342989
        - rid: 5393902103725342990
        - rid: 5393902103725342991
    - rid: 5393902103725342975
      type: {class: SmartSettings, ns: UnityEngine.Localization.SmartFormat.Core.Settings, asm: Unity.Localization}
      data:
        m_FormatErrorAction: 0
        m_ParseErrorAction: 0
        m_CaseSensitivity: 0
        m_ConvertCharacterStringLiterals: 1
    - rid: 5393902103725342976
      type: {class: Parser, ns: UnityEngine.Localization.SmartFormat.Core.Parsing, asm: Unity.Localization}
      data:
        m_OpeningBrace: 123
        m_ClosingBrace: 125
        m_Settings:
          rid: 5393902103725342975
        m_AlphanumericSelectors: 1
        m_AllowedSelectorChars: _-
        m_Operators: '[]().,'
        m_AlternativeEscaping: 0
        m_AlternativeEscapeChar: 92
    - rid: 5393902103725342977
      type: {class: ListFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Names:
        - list
        - l
        - 
        m_SmartSettings:
          rid: 5393902103725342975
    - rid: 5393902103725342978
      type: {class: PersistentVariablesSource, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Groups: []
    - rid: 5393902103725342979
      type: {class: DictionarySource, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data: 
    - rid: 5393902103725342980
      type: {class: ValueTupleSource, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data: 
    - rid: 5393902103725342981
      type: {class: XmlSource, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data: 
    - rid: 5393902103725342982
      type: {class: ReflectionSource, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data: 
    - rid: 5393902103725342983
      type: {class: DefaultSource, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data: 
    - rid: 5393902103725342984
      type: {class: PluralLocalizationFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Names:
        - plural
        - p
        - 
        m_DefaultTwoLetterISOLanguageName: en
    - rid: 5393902103725342985
      type: {class: ConditionalFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Names:
        - conditional
        - cond
        - 
    - rid: 5393902103725342986
      type: {class: TimeFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Names:
        - timespan
        - time
        - t
        - 
        m_DefaultFormatOptions: 4646
    - rid: 5393902103725342987
      type: {class: XElementFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Names:
        - xelement
        - xml
        - x
        - 
    - rid: 5393902103725342988
      type: {class: ChooseFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Names:
        - choose
        - c
        m_SplitChar: 124
    - rid: 5393902103725342989
      type: {class: SubStringFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Names:
        - substr
        m_ParameterDelimiter: 44
        m_NullDisplayString: (null)
        m_OutOfRangeBehavior: 0
    - rid: 5393902103725342990
      type: {class: IsMatchFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Names:
        - ismatch
    - rid: 5393902103725342991
      type: {class: DefaultFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions, asm: Unity.Localization}
      data:
        m_Names:
        - default
        - d
        - 
