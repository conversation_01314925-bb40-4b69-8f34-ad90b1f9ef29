<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.48.0 (20210717.1556)
 -->
<!-- Title: GenderExample Pages: 1 -->
<svg width="485pt" height="150pt"
 viewBox="0.00 0.00 484.50 150.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 146)">
<title>GenderExample</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-146 480.5,-146 480.5,4 -4,4"/>
<!-- theSmartString -->
<g id="node1" class="node">
<title>theSmartString</title>
<text text-anchor="start" x="44" y="-73.5" font-family="inter" font-size="10.00">{</text>
<text text-anchor="start" x="52" y="-73.5" font-family="inter" font-size="10.00" fill="#f37021">item</text>
<text text-anchor="start" x="77" y="-73.5" font-family="inter" font-size="10.00">.</text>
<text text-anchor="start" x="84" y="-73.5" font-family="inter" font-size="10.00" fill="#f37021">gender</text>
<text text-anchor="start" x="121" y="-73.5" font-family="inter" font-size="10.00">:</text>
<text text-anchor="start" x="128" y="-73.5" font-family="inter" font-size="10.00" fill="#67bc6b">choose</text>
<text text-anchor="start" x="166" y="-73.5" font-family="inter" font-size="10.00">(</text>
<text text-anchor="start" x="174" y="-73.5" font-family="inter" font-size="10.00" fill="#eb417a">Male</text>
<text text-anchor="start" x="200" y="-73.5" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="208" y="-73.5" font-family="inter" font-size="10.00" fill="#eb417a">Female</text>
<text text-anchor="start" x="246" y="-73.5" font-family="inter" font-size="10.00">)</text>
<text text-anchor="start" x="254" y="-73.5" font-family="inter" font-size="10.00" fill="#765ba7">El</text>
<text text-anchor="start" x="267" y="-73.5" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="275" y="-73.5" font-family="inter" font-size="10.00" fill="#765ba7">La</text>
<text text-anchor="start" x="290" y="-73.5" font-family="inter" font-size="10.00">} {</text>
<text text-anchor="start" x="304" y="-73.5" font-family="inter" font-size="10.00" fill="#f37021">item</text>
<text text-anchor="start" x="329" y="-73.5" font-family="inter" font-size="10.00">}</text>
<text text-anchor="start" x="337" y="-73.5" font-family="inter" font-size="10.00">es</text>
<text text-anchor="start" x="352" y="-73.5" font-family="inter" font-size="10.00">{</text>
<text text-anchor="start" x="360" y="-73.5" font-family="inter" font-size="10.00" fill="#f37021">item</text>
<text text-anchor="start" x="385" y="-73.5" font-family="inter" font-size="10.00">:{</text>
<text text-anchor="start" x="395" y="-73.5" font-family="inter" font-size="10.00" fill="#f37021">color</text>
<text text-anchor="start" x="422" y="-73.5" font-family="inter" font-size="10.00">}}</text>
</g>
<!-- theSmartString&#45;&gt;theSmartString -->
<g id="edge7" class="edge">
<title>theSmartString:maleOption&#45;&gt;theSmartString:maleOutput</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M180.94,-83.75C177.9,-95.76 185.34,-111 222,-111 248.63,-111 259.85,-102.95 262.82,-93.95"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="266.32,-93.83 263.06,-83.75 259.33,-93.66 266.32,-93.83"/>
</g>
<!-- theSmartString&#45;&gt;theSmartString -->
<g id="edge8" class="edge">
<title>theSmartString:femaleOption&#45;&gt;theSmartString:femaleOutput</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M220.86,-83.75C217.53,-95.76 223.52,-111 253,-111 274.42,-111 283.44,-102.95 285.49,-93.95"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="288.98,-93.62 285.14,-83.75 281.99,-93.87 288.98,-93.62"/>
</g>
<!-- selectorItem -->
<g id="node2" class="node">
<title>selectorItem</title>
<text text-anchor="start" x="57.5" y="-19.5" font-family="inter" font-size="10.00" fill="#f37021">Returns nested localized entry</text>
<text text-anchor="start" x="73.5" y="-8.5" font-family="inter" font-size="10.00" fill="#f37021">for `item` local variable</text>
</g>
<!-- selectorItem&#45;&gt;theSmartString -->
<g id="edge1" class="edge">
<title>selectorItem&#45;&gt;theSmartString:selectorItem1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M98.3,-27.57C84.02,-34.26 68.74,-44.48 63.71,-58.91"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="60.22,-58.56 62,-69 67.12,-59.73 60.22,-58.56"/>
</g>
<!-- selectorItem&#45;&gt;theSmartString -->
<g id="edge2" class="edge">
<title>selectorItem&#45;&gt;theSmartString:selectorItem2</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M196.74,-23.59C243.13,-29.37 298,-40.16 311.75,-59.5"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="308.45,-60.67 315,-69 315.08,-58.41 308.45,-60.67"/>
</g>
<!-- selectorGender -->
<g id="node3" class="node">
<title>selectorGender</title>
<text text-anchor="start" x="10.5" y="-134" font-family="inter" font-size="10.00" fill="#f37021">Extracts the gender</text>
<text text-anchor="start" x="0" y="-123" font-family="inter" font-size="10.00" fill="#f37021">metadata from the entry</text>
</g>
<!-- selectorGender&#45;&gt;theSmartString -->
<g id="edge3" class="edge">
<title>selectorGender&#45;&gt;theSmartString:selectorGender</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M74.66,-119.93C83.94,-113.76 94.02,-104.96 98.12,-94.08"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="101.6,-94.47 100,-84 94.72,-93.19 101.6,-94.47"/>
</g>
<!-- chooseName -->
<g id="node4" class="node">
<title>chooseName</title>
<text text-anchor="start" x="130" y="-128.5" font-family="inter" font-size="10.00" fill="#67bc6b">Use the choose formatter</text>
</g>
<!-- chooseName&#45;&gt;theSmartString -->
<g id="edge4" class="edge">
<title>chooseName&#45;&gt;theSmartString:chooseName</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M179.23,-125.27C168.6,-119.49 152.38,-108.66 146.89,-94.09"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="150.28,-93.19 145,-84 143.4,-94.47 150.28,-93.19"/>
</g>
<!-- selectorColor -->
<g id="node5" class="node">
<title>selectorColor</title>
<text text-anchor="start" x="337.5" y="-134" font-family="inter" font-size="10.00" fill="#f37021">Returns nested localized entry</text>
<text text-anchor="start" x="352.5" y="-123" font-family="inter" font-size="10.00" fill="#f37021">for `color` local variable</text>
</g>
<!-- selectorColor&#45;&gt;theSmartString -->
<g id="edge6" class="edge">
<title>selectorColor&#45;&gt;theSmartString:selectorColor</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M407,-119.77C407,-112.92 407,-103.47 407,-94.07"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="410.5,-94 407,-84 403.5,-94 410.5,-94"/>
</g>
<!-- scopeItem -->
<g id="node6" class="node">
<title>scopeItem</title>
<text text-anchor="start" x="289.5" y="-25" font-family="inter" font-size="10.00" fill="#f37021">Sets the current value to the `item`</text>
<text text-anchor="start" x="281" y="-14" font-family="inter" font-size="10.00" fill="#f37021">String Table entry. This will be available</text>
<text text-anchor="start" x="294" y="-3" font-family="inter" font-size="10.00" fill="#f37021">in the nested entry `color` scope.</text>
</g>
<!-- scopeItem&#45;&gt;theSmartString -->
<g id="edge5" class="edge">
<title>scopeItem&#45;&gt;theSmartString:selectorItem3</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M371,-33.28C371,-40.68 371,-49.86 371,-58.92"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="367.5,-59 371,-69 374.5,-59 367.5,-59"/>
</g>
</g>
</svg>
