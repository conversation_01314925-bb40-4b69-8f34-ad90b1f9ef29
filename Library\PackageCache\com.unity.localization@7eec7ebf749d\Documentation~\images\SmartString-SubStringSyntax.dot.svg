<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.48.0 (20210717.1556)
 -->
<!-- Title: SubStringFormatter Pages: 1 -->
<svg width="166pt" height="149pt"
 viewBox="0.00 0.00 166.00 149.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 145)">
<title>SubStringFormatter</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-145 162,-145 162,4 -4,4"/>
<!-- smartString -->
<g id="node1" class="node">
<title>smartString</title>
<text text-anchor="start" x="9" y="-73" font-family="inter" font-size="10.00">{</text>
<text text-anchor="start" x="15" y="-73" font-family="inter" font-size="10.00" fill="#f37021">value</text>
<text text-anchor="start" x="42" y="-73" font-family="inter" font-size="10.00">:</text>
<text text-anchor="start" x="47" y="-73" font-family="inter" font-size="10.00" fill="#67bc6b">substr</text>
<text text-anchor="start" x="77" y="-73" font-family="inter" font-size="10.00">(</text>
<text text-anchor="start" x="83" y="-73" font-family="inter" font-size="10.00" fill="#eb417a">start</text>
<text text-anchor="start" x="106" y="-73" font-family="inter" font-size="10.00">,</text>
<text text-anchor="start" x="111" y="-73" font-family="inter" font-size="10.00" fill="#eb417a">length</text>
<text text-anchor="start" x="142" y="-73" font-family="inter" font-size="10.00">)}</text>
</g>
<!-- selector -->
<g id="node2" class="node">
<title>selector</title>
<text text-anchor="middle" x="27" y="-133" font-family="inter" font-size="10.00" fill="#f37021">Any Value</text>
</g>
<!-- selector&#45;&gt;smartString -->
<g id="edge1" class="edge">
<title>selector&#45;&gt;smartString:selector1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M27,-129.95C27,-122.66 27,-107.67 27,-93"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="30.5,-93 27,-83 23.5,-93 30.5,-93"/>
</g>
<!-- formatterName -->
<g id="node3" class="node">
<title>formatterName</title>
<text text-anchor="middle" x="54" y="-8.5" font-family="inter" font-size="10.00" fill="#67bc6b">Formatter Name.</text>
</g>
<!-- formatterName&#45;&gt;smartString -->
<g id="edge2" class="edge">
<title>formatterName&#45;&gt;smartString:name</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M54.97,-16.62C56.57,-24.53 59.65,-41.41 60.67,-57.88"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="57.17,-58.12 61,-68 64.17,-57.89 57.17,-58.12"/>
</g>
<!-- start -->
<g id="node4" class="node">
<title>start</title>
<text text-anchor="middle" x="94" y="-133" font-family="inter" font-size="10.00" fill="#eb417a">Start Index</text>
</g>
<!-- start&#45;&gt;smartString -->
<g id="edge3" class="edge">
<title>start&#45;&gt;smartString:start</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M94,-129.95C94,-122.66 94,-107.67 94,-93"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="97.5,-93 94,-83 90.5,-93 97.5,-93"/>
</g>
<!-- length -->
<g id="node5" class="node">
<title>length</title>
<text text-anchor="middle" x="133" y="-14" font-family="inter" font-size="10.00" fill="#eb417a">Length</text>
<text text-anchor="middle" x="133" y="-3" font-family="inter" font-size="10.00" fill="#eb417a">(optional)</text>
</g>
<!-- length&#45;&gt;smartString -->
<g id="edge4" class="edge">
<title>length&#45;&gt;smartString:length</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M130.94,-22.27C129.29,-31.19 127.13,-44.73 126.32,-57.99"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="122.83,-57.89 126,-68 129.82,-58.12 122.83,-57.89"/>
</g>
</g>
</svg>
