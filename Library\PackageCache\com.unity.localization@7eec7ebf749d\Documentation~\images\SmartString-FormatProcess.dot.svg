<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.48.0 (20210717.1556)
 -->
<!-- Title: SmartFormat Pages: 1 -->
<svg width="921pt" height="1029pt"
 viewBox="0.00 0.00 920.50 1029.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1025)">
<title>SmartFormat</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-1025 916.5,-1025 916.5,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_foreachFormatItem</title>
<path fill="#daedfd" stroke="#2196f3" d="M20,-8C20,-8 745,-8 745,-8 751,-8 757,-14 757,-20 757,-20 757,-873 757,-873 757,-879 751,-885 745,-885 745,-885 20,-885 20,-885 14,-885 8,-879 8,-873 8,-873 8,-20 8,-20 8,-14 14,-8 20,-8"/>
<text text-anchor="middle" x="71.5" y="-873" font-family="inter" font-size="10.00">&lt;For each Format Item&gt;</text>
</g>
<g id="clust3" class="cluster">
<title>cluster_formatters</title>
<path fill="#fff7e1" stroke="#ffcb27" d="M283,-81C283,-81 555,-81 555,-81 561,-81 567,-87 567,-93 567,-93 567,-514 567,-514 567,-520 561,-526 555,-526 555,-526 283,-526 283,-526 277,-526 271,-520 271,-514 271,-514 271,-93 271,-93 271,-87 277,-81 283,-81"/>
<text text-anchor="middle" x="329" y="-514" font-family="inter" font-size="10.00">Format The Item</text>
<text text-anchor="middle" x="329" y="-503" font-family="inter" font-size="10.00">&lt;For each Formatter&gt;</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_selectors</title>
<path fill="#fff7e1" stroke="#ffcb27" d="M446,-674C446,-674 598,-674 598,-674 604,-674 610,-680 610,-686 610,-686 610,-749 610,-749 610,-755 604,-761 598,-761 598,-761 446,-761 446,-761 440,-761 434,-755 434,-749 434,-749 434,-686 434,-686 434,-680 440,-674 446,-674"/>
<text text-anchor="middle" x="489" y="-749" font-family="inter" font-size="10.00">Evaluate Selectors</text>
<text text-anchor="middle" x="489" y="-738" font-family="inter" font-size="10.00">&lt;For each Selector&gt;</text>
</g>
<!-- applySmartFormating -->
<g id="node1" class="node">
<title>applySmartFormating</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M594,-1021C594,-1021 540,-1021 540,-1021 534,-1021 528,-1015 528,-1009 528,-1009 528,-997 528,-997 528,-991 534,-985 540,-985 540,-985 594,-985 594,-985 600,-985 606,-991 606,-997 606,-997 606,-1009 606,-1009 606,-1015 600,-1021 594,-1021"/>
<text text-anchor="middle" x="567" y="-1000.5" font-family="inter" font-size="10.00">Smart Format</text>
</g>
<!-- parse -->
<g id="node2" class="node">
<title>parse</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M582,-948C582,-948 552,-948 552,-948 546,-948 540,-942 540,-936 540,-936 540,-924 540,-924 540,-918 546,-912 552,-912 552,-912 582,-912 582,-912 588,-912 594,-918 594,-924 594,-924 594,-936 594,-936 594,-942 588,-948 582,-948"/>
<text text-anchor="middle" x="567" y="-927.5" font-family="inter" font-size="10.00">Parse</text>
</g>
<!-- applySmartFormating&#45;&gt;parse -->
<g id="edge1" class="edge">
<title>applySmartFormating&#45;&gt;parse</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M567,-984.81C567,-976.79 567,-967.05 567,-958.07"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="570.5,-958.03 567,-948.03 563.5,-958.03 570.5,-958.03"/>
</g>
<!-- isLiteralText -->
<g id="node3" class="node">
<title>isLiteralText</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M556.51,-852.17C556.51,-852.17 523.49,-833.83 523.49,-833.83 518.24,-830.91 518.24,-825.09 523.49,-822.17 523.49,-822.17 556.51,-803.83 556.51,-803.83 561.76,-800.91 572.24,-800.91 577.49,-803.83 577.49,-803.83 610.51,-822.17 610.51,-822.17 615.76,-825.09 615.76,-830.91 610.51,-833.83 610.51,-833.83 577.49,-852.17 577.49,-852.17 572.24,-855.09 561.76,-855.09 556.51,-852.17"/>
<text text-anchor="middle" x="567" y="-831" font-family="inter" font-size="10.00">Is Literal</text>
<text text-anchor="middle" x="567" y="-820" font-family="inter" font-size="10.00">Text?</text>
</g>
<!-- parse&#45;&gt;isLiteralText -->
<g id="edge2" class="edge">
<title>parse&#45;&gt;isLiteralText</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M567,-911.58C567,-899.67 567,-883.39 567,-868.43"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="570.5,-868.35 567,-858.35 563.5,-868.35 570.5,-868.35"/>
</g>
<!-- evaluateSelectors -->
<g id="node4" class="node">
<title>evaluateSelectors</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M590,-723C590,-723 454,-723 454,-723 448,-723 442,-717 442,-711 442,-711 442,-694 442,-694 442,-688 448,-682 454,-682 454,-682 590,-682 590,-682 596,-682 602,-688 602,-694 602,-694 602,-711 602,-711 602,-717 596,-723 590,-723"/>
<text text-anchor="middle" x="522" y="-711" font-family="inter" font-size="10.00">Try each Source against the</text>
<text text-anchor="middle" x="522" y="-700" font-family="inter" font-size="10.00">Format Item until one succeeds</text>
<text text-anchor="middle" x="522" y="-689" font-family="inter" font-size="10.00">and extracts the Current Value</text>
</g>
<!-- isLiteralText&#45;&gt;evaluateSelectors -->
<g id="edge4" class="edge">
<title>isLiteralText&#45;&gt;evaluateSelectors</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M558.11,-802.6C550.81,-782.57 540.46,-754.15 532.7,-732.88"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="535.95,-731.57 529.24,-723.37 529.38,-733.96 535.95,-731.57"/>
<text text-anchor="middle" x="556" y="-772" font-family="inter" font-size="10.00">No</text>
</g>
<!-- write -->
<g id="node16" class="node">
<title>write</title>
<path fill="#e9f4e9" stroke="#67bc6b" d="M702,-720.5C702,-720.5 632,-720.5 632,-720.5 626,-720.5 620,-714.5 620,-708.5 620,-708.5 620,-696.5 620,-696.5 620,-690.5 626,-684.5 632,-684.5 632,-684.5 702,-684.5 702,-684.5 708,-684.5 714,-690.5 714,-696.5 714,-696.5 714,-708.5 714,-708.5 714,-714.5 708,-720.5 702,-720.5"/>
<text text-anchor="middle" x="667" y="-700" font-family="inter" font-size="10.00">Write into output</text>
</g>
<!-- isLiteralText&#45;&gt;write -->
<g id="edge3" class="edge">
<title>isLiteralText&#45;&gt;write</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M583.29,-806.88C600.54,-785.58 627.82,-751.89 646.51,-728.8"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="649.41,-730.79 652.98,-720.82 643.97,-726.38 649.41,-730.79"/>
<text text-anchor="middle" x="619.5" y="-772" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- selectorUsed -->
<g id="node5" class="node">
<title>selectorUsed</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M510.63,-641.15C510.63,-641.15 412.37,-607.85 412.37,-607.85 406.68,-605.93 406.68,-602.07 412.37,-600.15 412.37,-600.15 510.63,-566.85 510.63,-566.85 516.32,-564.93 527.68,-564.93 533.37,-566.85 533.37,-566.85 631.63,-600.15 631.63,-600.15 637.32,-602.07 637.32,-605.93 631.63,-607.85 631.63,-607.85 533.37,-641.15 533.37,-641.15 527.68,-643.07 516.32,-643.07 510.63,-641.15"/>
<text text-anchor="middle" x="522" y="-612.5" font-family="inter" font-size="10.00">Were any of the</text>
<text text-anchor="middle" x="522" y="-601.5" font-family="inter" font-size="10.00">Sources able to handle</text>
<text text-anchor="middle" x="522" y="-590.5" font-family="inter" font-size="10.00">the Selector?</text>
</g>
<!-- evaluateSelectors&#45;&gt;selectorUsed -->
<g id="edge5" class="edge">
<title>evaluateSelectors&#45;&gt;selectorUsed</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M522,-674C522,-668.17 522,-661.83 522,-655.44"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="525.5,-655.23 522,-645.23 518.5,-655.23 525.5,-655.23"/>
</g>
<!-- nameMatch -->
<g id="node6" class="node">
<title>nameMatch</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M407.48,-484.63C407.48,-484.63 290.52,-450.37 290.52,-450.37 284.76,-448.69 284.76,-445.31 290.52,-443.63 290.52,-443.63 407.48,-409.37 407.48,-409.37 413.24,-407.69 424.76,-407.69 430.52,-409.37 430.52,-409.37 547.48,-443.63 547.48,-443.63 553.24,-445.31 553.24,-448.69 547.48,-450.37 547.48,-450.37 430.52,-484.63 430.52,-484.63 424.76,-486.31 413.24,-486.31 407.48,-484.63"/>
<text text-anchor="middle" x="419" y="-455.5" font-family="inter" font-size="10.00">Does the formatter name</text>
<text text-anchor="middle" x="419" y="-444.5" font-family="inter" font-size="10.00">match any of the</text>
<text text-anchor="middle" x="419" y="-433.5" font-family="inter" font-size="10.00">current Formatters Names?</text>
</g>
<!-- selectorUsed&#45;&gt;nameMatch -->
<g id="edge7" class="edge">
<title>selectorUsed&#45;&gt;nameMatch</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M500.16,-570.13C484.68,-546.84 463.78,-515.39 447.07,-490.24"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="449.8,-488.02 441.35,-481.63 443.97,-491.89 449.8,-488.02"/>
<text text-anchor="middle" x="490.5" y="-537" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- noSelector -->
<g id="node13" class="node">
<title>noSelector</title>
<path fill="#fce5ec" stroke="#eb417a" d="M736.5,-125C736.5,-125 587.5,-125 587.5,-125 581.5,-125 575.5,-119 575.5,-113 575.5,-113 575.5,-101 575.5,-101 575.5,-95 581.5,-89 587.5,-89 587.5,-89 736.5,-89 736.5,-89 742.5,-89 748.5,-95 748.5,-101 748.5,-101 748.5,-113 748.5,-113 748.5,-119 742.5,-125 736.5,-125"/>
<text text-anchor="middle" x="662" y="-110" font-family="inter" font-size="10.00">Report error:</text>
<text text-anchor="middle" x="662" y="-99" font-family="inter" font-size="10.00">&quot;Could not evaluate the selector...&quot;</text>
</g>
<!-- selectorUsed&#45;&gt;noSelector -->
<g id="edge6" class="edge">
<title>selectorUsed&#45;&gt;noSelector</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M556.67,-574.55C587.26,-546.06 627,-499.19 627,-448 627,-448 627,-448 627,-223 627,-191.55 639.13,-157.28 649.11,-134.4"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="652.41,-135.59 653.35,-125.04 646.03,-132.7 652.41,-135.59"/>
<text text-anchor="middle" x="634" y="-338.5" font-family="inter" font-size="10.00">No</text>
</g>
<!-- nextFormater -->
<g id="node7" class="node">
<title>nextFormater</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M545,-125C545,-125 449,-125 449,-125 443,-125 437,-119 437,-113 437,-113 437,-101 437,-101 437,-95 443,-89 449,-89 449,-89 545,-89 545,-89 551,-89 557,-95 557,-101 557,-101 557,-113 557,-113 557,-119 551,-125 545,-125"/>
<text text-anchor="middle" x="497" y="-104.5" font-family="inter" font-size="10.00">Try the next Formatter</text>
</g>
<!-- nameMatch&#45;&gt;nextFormater -->
<g id="edge9" class="edge">
<title>nameMatch&#45;&gt;nextFormater</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M445,-413.55C469.3,-380.98 503.89,-328.26 518,-276 530.05,-231.38 525.3,-217.64 518,-172 515.99,-159.45 511.9,-146.01 507.86,-134.71"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="511.01,-133.15 504.22,-125.02 504.46,-135.61 511.01,-133.15"/>
<text text-anchor="middle" x="518" y="-297" font-family="inter" font-size="10.00">No</text>
</g>
<!-- evaluateFormatters -->
<g id="node8" class="node">
<title>evaluateFormatters</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M448,-359C448,-359 340,-359 340,-359 334,-359 328,-353 328,-347 328,-347 328,-335 328,-335 328,-329 334,-323 340,-323 340,-323 448,-323 448,-323 454,-323 460,-329 460,-335 460,-335 460,-347 460,-347 460,-353 454,-359 448,-359"/>
<text text-anchor="middle" x="394" y="-344" font-family="inter" font-size="10.00">Try Formatter against the</text>
<text text-anchor="middle" x="394" y="-333" font-family="inter" font-size="10.00">Current Value</text>
</g>
<!-- nameMatch&#45;&gt;evaluateFormatters -->
<g id="edge10" class="edge">
<title>nameMatch&#45;&gt;evaluateFormatters</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M409.97,-408.45C406.84,-395.4 403.38,-381.03 400.49,-369.01"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="403.86,-368.03 398.12,-359.13 397.05,-369.67 403.86,-368.03"/>
<text text-anchor="middle" x="413.5" y="-380" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- formatterUsed -->
<g id="node9" class="node">
<title>formatterUsed</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M382.39,-250.97C382.39,-250.97 290.61,-227.03 290.61,-227.03 284.81,-225.51 284.81,-222.49 290.61,-220.97 290.61,-220.97 382.39,-197.03 382.39,-197.03 388.19,-195.51 399.81,-195.51 405.61,-197.03 405.61,-197.03 497.39,-220.97 497.39,-220.97 503.19,-222.49 503.19,-225.51 497.39,-227.03 497.39,-227.03 405.61,-250.97 405.61,-250.97 399.81,-252.49 388.19,-252.49 382.39,-250.97"/>
<text text-anchor="middle" x="394" y="-227" font-family="inter" font-size="10.00">Can Formatter handle</text>
<text text-anchor="middle" x="394" y="-216" font-family="inter" font-size="10.00">the Current Value?</text>
</g>
<!-- evaluateFormatters&#45;&gt;formatterUsed -->
<g id="edge11" class="edge">
<title>evaluateFormatters&#45;&gt;formatterUsed</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M394,-322.53C394,-307.12 394,-284.1 394,-264.29"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="397.5,-264.28 394,-254.28 390.5,-264.28 397.5,-264.28"/>
</g>
<!-- anyFormatterUsed -->
<g id="node10" class="node">
<title>anyFormatterUsed</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M149.33,-270.51C149.33,-270.51 69.67,-229.49 69.67,-229.49 64.33,-226.75 64.33,-221.25 69.67,-218.51 69.67,-218.51 149.33,-177.49 149.33,-177.49 154.67,-174.75 165.33,-174.75 170.67,-177.49 170.67,-177.49 250.33,-218.51 250.33,-218.51 255.67,-221.25 255.67,-226.75 250.33,-229.49 250.33,-229.49 170.67,-270.51 170.67,-270.51 165.33,-273.25 154.67,-273.25 149.33,-270.51"/>
<text text-anchor="middle" x="160" y="-238" font-family="inter" font-size="10.00">Were any of the</text>
<text text-anchor="middle" x="160" y="-227" font-family="inter" font-size="10.00">Formatters able to</text>
<text text-anchor="middle" x="160" y="-216" font-family="inter" font-size="10.00">handle the</text>
<text text-anchor="middle" x="160" y="-205" font-family="inter" font-size="10.00">Selector?</text>
</g>
<!-- evaluateFormatters&#45;&gt;anyFormatterUsed -->
<g id="edge14" class="edge">
<title>evaluateFormatters&#45;&gt;anyFormatterUsed</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M271,-279.55C253.34,-270.87 235.86,-262.28 220.18,-254.58"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="221.52,-251.33 211,-250.07 218.43,-257.62 221.52,-251.33"/>
</g>
<!-- formatterUsed&#45;&gt;nextFormater -->
<g id="edge12" class="edge">
<title>formatterUsed&#45;&gt;nextFormater</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M415.09,-199.45C432.47,-180.04 457.05,-152.6 474.74,-132.85"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="477.56,-134.95 481.63,-125.16 472.35,-130.28 477.56,-134.95"/>
<text text-anchor="middle" x="470" y="-146" font-family="inter" font-size="10.00">No</text>
</g>
<!-- writeFormat -->
<g id="node15" class="node">
<title>writeFormat</title>
<path fill="#e9f4e9" stroke="#67bc6b" d="M407,-125C407,-125 337,-125 337,-125 331,-125 325,-119 325,-113 325,-113 325,-101 325,-101 325,-95 331,-89 337,-89 337,-89 407,-89 407,-89 413,-89 419,-95 419,-101 419,-101 419,-113 419,-113 419,-119 413,-125 407,-125"/>
<text text-anchor="middle" x="372" y="-104.5" font-family="inter" font-size="10.00">Write into output</text>
</g>
<!-- formatterUsed&#45;&gt;writeFormat -->
<g id="edge13" class="edge">
<title>formatterUsed&#45;&gt;writeFormat</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M388.67,-195.16C385.19,-176.94 380.65,-153.23 377.19,-135.15"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="380.61,-134.4 375.3,-125.23 373.74,-135.71 380.61,-134.4"/>
<text text-anchor="middle" x="388.5" y="-146" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- nextFormatItem -->
<g id="node12" class="node">
<title>nextFormatItem</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M203,-52C203,-52 129,-52 129,-52 123,-52 117,-46 117,-40 117,-40 117,-28 117,-28 117,-22 123,-16 129,-16 129,-16 203,-16 203,-16 209,-16 215,-22 215,-28 215,-28 215,-40 215,-40 215,-46 209,-52 203,-52"/>
<text text-anchor="middle" x="166" y="-31.5" font-family="inter" font-size="10.00">Next Format Item</text>
</g>
<!-- anyFormatterUsed&#45;&gt;nextFormatItem -->
<g id="edge16" class="edge">
<title>anyFormatterUsed&#45;&gt;nextFormatItem</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M111.92,-196.56C67.86,-168.81 13.6,-123.39 43,-81 57.62,-59.93 83.14,-48.49 107.16,-42.29"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="108.02,-45.69 116.97,-40.02 106.44,-38.87 108.02,-45.69"/>
<text text-anchor="middle" x="51.5" y="-104.5" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- noFormatter -->
<g id="node14" class="node">
<title>noFormatter</title>
<path fill="#fce5ec" stroke="#eb417a" d="M251,-125C251,-125 81,-125 81,-125 75,-125 69,-119 69,-113 69,-113 69,-101 69,-101 69,-95 75,-89 81,-89 81,-89 251,-89 251,-89 257,-89 263,-95 263,-101 263,-101 263,-113 263,-113 263,-119 257,-125 251,-125"/>
<text text-anchor="middle" x="166" y="-110" font-family="inter" font-size="10.00">Report error:</text>
<text text-anchor="middle" x="166" y="-99" font-family="inter" font-size="10.00">&quot;No suitable Formatter could be found&quot;</text>
</g>
<!-- anyFormatterUsed&#45;&gt;noFormatter -->
<g id="edge15" class="edge">
<title>anyFormatterUsed&#45;&gt;noFormatter</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M162.6,-173.23C163.28,-160.16 163.99,-146.56 164.58,-135.17"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="168.09,-135.2 165.11,-125.03 161.1,-134.83 168.09,-135.2"/>
<text text-anchor="middle" x="171" y="-146" font-family="inter" font-size="10.00">No</text>
</g>
<!-- return -->
<g id="node11" class="node">
<title>return</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M900.5,-52C900.5,-52 777.5,-52 777.5,-52 771.5,-52 765.5,-46 765.5,-40 765.5,-40 765.5,-28 765.5,-28 765.5,-22 771.5,-16 777.5,-16 777.5,-16 900.5,-16 900.5,-16 906.5,-16 912.5,-22 912.5,-28 912.5,-28 912.5,-40 912.5,-40 912.5,-46 906.5,-52 900.5,-52"/>
<text text-anchor="middle" x="839" y="-31.5" font-family="inter" font-size="10.00">Return the output as a String</text>
</g>
<!-- noSelector&#45;&gt;nextFormatItem -->
<g id="edge8" class="edge">
<title>noSelector&#45;&gt;nextFormatItem</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M605.04,-88.9C593.83,-85.95 582.1,-83.14 571,-81 449.48,-57.55 304.97,-44.67 225.6,-38.87"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="225.54,-35.35 215.31,-38.13 225.03,-42.33 225.54,-35.35"/>
</g>
<!-- noFormatter&#45;&gt;nextFormatItem -->
<g id="edge17" class="edge">
<title>noFormatter&#45;&gt;nextFormatItem</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M166,-88.81C166,-80.79 166,-71.05 166,-62.07"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="169.5,-62.03 166,-52.03 162.5,-62.03 169.5,-62.03"/>
</g>
<!-- writeFormat&#45;&gt;return -->
<g id="edge18" class="edge">
<title>writeFormat&#45;&gt;return</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M757,-71.88C758.37,-71.6 759.7,-71.31 761,-71 774.3,-67.87 788.08,-62.37 800.28,-56.59"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="801.85,-59.72 809.27,-52.16 798.76,-53.44 801.85,-59.72"/>
</g>
</g>
</svg>
