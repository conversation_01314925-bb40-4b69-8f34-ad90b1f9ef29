# About Localization

Use the Localization package to configure localization settings for your application.

Add support for multiple languages and regional variants, including:

- String localization: Set different strings to display based on locale. Use the Smart Strings feature to add logic to automatically replace specific strings, such as placeholders and plurals.
- Asset localization: Use a different asset (such as a texture, model, or audio file) based on a locale.
- Pseudo-localization: Test how your project will adapt to different localizations at an early stage, before adding your translations.
- Import and export localization data to XLIFF, CSV and Google Sheets.

Add localization to your projects using the Localization package to help make your applications more accessible to a wider audience.

To get started, check the [Installation](Installation.md) and [Quick Start Guide](QuickStartGuide.md) sections.

## What's new in Localization

For information on what's new in the latest version of Localization, see section [What's new](whats-new.md).

## Upgrading

For information on upgrading from a previous version of the Localization Package to the current version see [Upgrade guide](upgrade-guide.md)

## Feedback

Your feedback is welcome [here](https://forum.unity.com/forums/localisation-tools-previews.205/)
