using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Settings;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Manages language switching and localization for the game
/// </summary>
public class LocalizationManager : MonoBehaviour
{
    [Header("Localization Settings")]
    public LocalizedStringTable quizStringTable;
    
    private static LocalizationManager instance;
    public static LocalizationManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindFirstObjectByType<LocalizationManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("LocalizationManager");
                    instance = go.AddComponent<LocalizationManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // Initialize localization
        StartCoroutine(InitializeLocalization());
    }

    private IEnumerator InitializeLocalization()
    {
        // Wait for localization to initialize
        yield return LocalizationSettings.InitializationOperation;
        
        // Load saved language preference
        LoadLanguagePreference();
    }

    /// <summary>
    /// Changes the current language
    /// </summary>
    /// <param name="localeCode">Language code (e.g., "en", "fr", "es")</param>
    public void ChangeLanguage(string localeCode)
    {
        StartCoroutine(ChangeLanguageCoroutine(localeCode));
    }

    private IEnumerator ChangeLanguageCoroutine(string localeCode)
    {
        var locale = LocalizationSettings.AvailableLocales.GetLocale(localeCode);
        if (locale != null)
        {
            LocalizationSettings.SelectedLocale = locale;
            SaveLanguagePreference(localeCode);
            Debug.Log($"Language changed to: {locale.LocaleName}");
        }
        else
        {
            Debug.LogWarning($"Locale '{localeCode}' not found!");
        }
        yield return null;
    }

    /// <summary>
    /// Gets available languages
    /// </summary>
    /// <returns>List of available locale codes</returns>
    public List<string> GetAvailableLanguages()
    {
        List<string> languages = new List<string>();
        foreach (var locale in LocalizationSettings.AvailableLocales.Locales)
        {
            languages.Add(locale.Identifier.Code);
        }
        return languages;
    }

    /// <summary>
    /// Gets the current language code
    /// </summary>
    /// <returns>Current language code</returns>
    public string GetCurrentLanguage()
    {
        return LocalizationSettings.SelectedLocale?.Identifier.Code ?? "en";
    }

    /// <summary>
    /// Gets a localized quiz question
    /// </summary>
    /// <param name="questionKey">The key for the question</param>
    /// <returns>Localized question text</returns>
    public string GetLocalizedQuizQuestion(string questionKey)
    {
        if (quizStringTable != null)
        {
            var localizedString = quizStringTable.GetTable().GetEntry(questionKey);
            return localizedString?.GetLocalizedString() ?? questionKey;
        }
        return questionKey;
    }

    /// <summary>
    /// Saves language preference to PlayerPrefs
    /// </summary>
    /// <param name="localeCode">Language code to save</param>
    private void SaveLanguagePreference(string localeCode)
    {
        PlayerPrefs.SetString("SelectedLanguage", localeCode);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Loads language preference from PlayerPrefs
    /// </summary>
    private void LoadLanguagePreference()
    {
        string savedLanguage = PlayerPrefs.GetString("SelectedLanguage", "en");
        ChangeLanguage(savedLanguage);
    }
}
