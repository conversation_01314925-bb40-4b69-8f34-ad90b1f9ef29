<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.48.0 (20210717.1556)
 -->
<!-- Title: PluralFormatter Pages: 1 -->
<svg width="267pt" height="160pt"
 viewBox="0.00 0.00 267.00 160.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 156)">
<title>PluralFormatter</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-156 263,-156 263,4 -4,4"/>
<!-- smartString -->
<g id="node1" class="node">
<title>smartString</title>
<text text-anchor="start" x="10" y="-73" font-family="inter" font-size="10.00">{</text>
<text text-anchor="start" x="16" y="-73" font-family="inter" font-size="10.00" fill="#f37021">value</text>
<text text-anchor="start" x="43" y="-73" font-family="inter" font-size="10.00">:</text>
<text text-anchor="start" x="48" y="-73" font-family="inter" font-size="10.00" fill="#67bc6b">plural</text>
<text text-anchor="start" x="75" y="-73" font-family="inter" font-size="10.00">:</text>
<text text-anchor="start" x="80" y="-73" font-family="inter" font-size="10.00" fill="#765ba7">zero</text>
<text text-anchor="start" x="103" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="109" y="-73" font-family="inter" font-size="10.00" fill="#765ba7">one</text>
<text text-anchor="start" x="129" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="135" y="-73" font-family="inter" font-size="10.00" fill="#765ba7">two</text>
<text text-anchor="start" x="155" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="161" y="-73" font-family="inter" font-size="10.00" fill="#765ba7">few</text>
<text text-anchor="start" x="180" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="186" y="-73" font-family="inter" font-size="10.00" fill="#765ba7">many</text>
<text text-anchor="start" x="213" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="219" y="-73" font-family="inter" font-size="10.00" fill="#765ba7">other</text>
<text text-anchor="start" x="246" y="-73" font-family="inter" font-size="10.00">}</text>
</g>
<!-- selector -->
<g id="node2" class="node">
<title>selector</title>
<text text-anchor="middle" x="28" y="-138.5" font-family="inter" font-size="10.00" fill="#f37021">Any Number</text>
</g>
<!-- selector&#45;&gt;smartString -->
<g id="edge1" class="edge">
<title>selector&#45;&gt;smartString:selector1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M28,-135.27C28,-127.18 28,-109.95 28,-93.24"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="31.5,-93 28,-83 24.5,-93 31.5,-93"/>
</g>
<!-- formatterName -->
<g id="node3" class="node">
<title>formatterName</title>
<text text-anchor="middle" x="60" y="-14" font-family="inter" font-size="10.00" fill="#67bc6b">Formatter Name</text>
<text text-anchor="middle" x="60" y="-3" font-family="inter" font-size="10.00" fill="#67bc6b">&quot;plural&quot; or &quot;p&quot; or implicit</text>
</g>
<!-- formatterName&#45;&gt;smartString -->
<g id="edge2" class="edge">
<title>formatterName&#45;&gt;smartString:name</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M60,-22.32C60,-31.19 60,-44.57 60,-57.66"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="56.5,-58 60,-68 63.5,-58 56.5,-58"/>
</g>
<!-- plurals -->
<g id="node4" class="node">
<title>plurals</title>
<text text-anchor="middle" x="170" y="-144" font-family="inter" font-size="10.00" fill="#765ba7">Plural Arguments.</text>
<text text-anchor="middle" x="170" y="-133" font-family="inter" font-size="10.00" fill="#765ba7">Smallest&#45;to&#45;highest order</text>
</g>
<!-- plurals&#45;&gt;smartString -->
<g id="edge3" class="edge">
<title>plurals&#45;&gt;smartString:plural1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M134.68,-129.83C116.43,-122.53 96.86,-110.77 91.45,-92.91"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="94.92,-92.39 90,-83 87.99,-93.4 94.92,-92.39"/>
</g>
<!-- plurals&#45;&gt;smartString -->
<g id="edge4" class="edge">
<title>plurals&#45;&gt;smartString:plural2</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M151.81,-129.89C139.39,-121.75 124.36,-109.08 119.55,-93.25"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="122.96,-92.36 118,-83 116.04,-93.41 122.96,-92.36"/>
</g>
<!-- plurals&#45;&gt;smartString -->
<g id="edge5" class="edge">
<title>plurals&#45;&gt;smartString:plural3</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M170,-129.91C170,-120.83 170,-106.86 170,-93.25"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="173.5,-93 170,-83 166.5,-93 173.5,-93"/>
</g>
<!-- plurals&#45;&gt;smartString -->
<g id="edge6" class="edge">
<title>plurals&#45;&gt;smartString:plural4</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M178.86,-129.86C185.79,-121.07 194.75,-107.5 197.87,-93.08"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="201.37,-93.33 199,-83 194.41,-92.55 201.37,-93.33"/>
</g>
<!-- plurals&#45;&gt;smartString -->
<g id="edge7" class="edge">
<title>plurals&#45;&gt;smartString:plural5</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M193.44,-129.87C208.12,-122.05 225.2,-109.78 230.41,-93.23"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="233.92,-93.42 232,-83 227.01,-92.34 233.92,-93.42"/>
</g>
</g>
</svg>
