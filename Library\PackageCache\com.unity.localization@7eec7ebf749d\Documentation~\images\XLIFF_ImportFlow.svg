<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:lucid="lucid" width="1070.94" height="1446.47"><g transform="translate(-323.4375 -18.612103889699426)" lucid:page-tab-id="7ezayB~FQlW7"><path d="M775 46.6c0-4.4 3.58-8 8-8h154c4.42 0 8 3.6 8 8v83.7c0 4.4-3.58 8-8 8H783c-4.42 0-8-3.6-8-8z" stroke="#333" stroke-width="4" fill="#83bbe5"/><use xlink:href="#a" transform="matrix(1,0,0,1,780,43.612103889699426) translate(9.674999999999997 48.15)"/><use xlink:href="#b" transform="matrix(1,0,0,1,780,43.612103889699426) translate(70.525 48.15)"/><use xlink:href="#c" transform="matrix(1,0,0,1,780,43.612103889699426) translate(125.37500000000001 48.15)"/><path d="M860 142.3v45.56" stroke="#333" stroke-width="4" fill="none"/><path d="M862 142.34h-4v-2.05h4z" fill="#333"/><path d="M860 204.13l-4.64-14.27h9.28z" stroke="#333" stroke-width="4" fill="#333"/><path d="M862 418.02h-4v-13.4h4zm0-32.08h-4V369.7h4z" fill="#333"/><path d="M861.24 367.76l.76-.16v2.15h-4v-2.16zM860 434.28l-4.64-14.26h9.28z" fill="#333"/><path d="M860 440.76l-7.4-22.74h14.8zm-1.88-18.74l1.88 5.8 1.88-5.8z" fill="#333"/><use xlink:href="#d" transform="matrix(1,0,0,1,848.354938271605,385.94042218928973) translate(0 12.444444444444446)"/><path d="M853.4 214.38c3.65-2.5 9.55-2.5 13.2 0l101.8 70.22c3.65 2.5 3.65 6.58 0 9.1l-101.8 70.2c-3.65 2.52-9.55 2.52-13.2 0l-101.8-70.2c-3.65-2.52-3.65-6.6 0-9.1z" stroke="#333" stroke-width="4" fill="#83bbe5"/><use xlink:href="#e" transform="matrix(1,0,0,1,750,214.84271861145817) translate(47.55685185185184 55.77291666666667)"/><use xlink:href="#f" transform="matrix(1,0,0,1,750,214.84271861145817) translate(94.64574074074073 55.77291666666667)"/><use xlink:href="#g" transform="matrix(1,0,0,1,750,214.84271861145817) translate(125.36425925925926 55.77291666666667)"/><use xlink:href="#h" transform="matrix(1,0,0,1,750,214.84271861145817) translate(155.16796296296297 55.77291666666667)"/><use xlink:href="#i" transform="matrix(1,0,0,1,750,214.84271861145817) translate(25.60129629629629 76.57291666666667)"/><use xlink:href="#j" transform="matrix(1,0,0,1,750,214.84271861145817) translate(81.35685185185186 76.57291666666667)"/><use xlink:href="#k" transform="matrix(1,0,0,1,750,214.84271861145817) translate(95.8012962962963 76.57291666666667)"/><use xlink:href="#l" transform="matrix(1,0,0,1,750,214.84271861145817) translate(150.54574074074074 76.57291666666667)"/><use xlink:href="#m" transform="matrix(1,0,0,1,750,214.84271861145817) translate(83.11425925925926 97.37291666666668)"/><path d="M610.94 1340.88c36.93 0 66.87 23.33 66.87 52.1 0 28.77-29.93 52.1-66.86 52.1H410.3c-36.92 0-66.86-23.33-66.86-52.1 0-28.77 29.94-52.1 66.87-52.1z" stroke="#333" stroke-width="4" fill="#a3d977"/><use xlink:href="#n" transform="matrix(1,0,0,1,348.4375,1345.8800050763043) translate(0.3129629629629278 49.49166666666667)"/><use xlink:href="#f" transform="matrix(1,0,0,1,348.4375,1345.8800050763043) translate(36.85740740740738 49.49166666666667)"/><use xlink:href="#o" transform="matrix(1,0,0,1,348.4375,1345.8800050763043) translate(67.5759259259259 49.49166666666667)"/><use xlink:href="#k" transform="matrix(1,0,0,1,348.4375,1345.8800050763043) translate(137.72777777777776 49.49166666666667)"/><use xlink:href="#p" transform="matrix(1,0,0,1,348.4375,1345.8800050763043) translate(192.4722222222222 49.49166666666667)"/><use xlink:href="#q" transform="matrix(1,0,0,1,348.4375,1345.8800050763043) translate(241.15 49.49166666666667)"/><path d="M503.76 444.1c3.8-2.26 9.94-2.26 13.73 0l119.07 71.1c3.8 2.27 3.8 5.94 0 8.2L517.5 594.5c-3.8 2.27-9.95 2.27-13.74 0l-119.08-71.1c-3.8-2.26-3.8-5.93 0-8.2z" stroke="#333" stroke-width="4" fill="#83bbe5"/><use xlink:href="#r" transform="matrix(1,0,0,1,382.8125,445) translate(53.712407407407404 55.77291666666667)"/><use xlink:href="#f" transform="matrix(1,0,0,1,382.8125,445) translate(72.97166666666666 55.77291666666667)"/><use xlink:href="#k" transform="matrix(1,0,0,1,382.8125,445) translate(103.69018518518519 55.77291666666667)"/><use xlink:href="#l" transform="matrix(1,0,0,1,382.8125,445) translate(158.43462962962963 55.77291666666667)"/><use xlink:href="#s" transform="matrix(1,0,0,1,382.8125,445) translate(42.20499999999999 76.57291666666667)"/><use xlink:href="#t" transform="matrix(1,0,0,1,382.8125,445) translate(79.66425925925925 76.57291666666667)"/><use xlink:href="#j" transform="matrix(1,0,0,1,382.8125,445) translate(100.75314814814814 76.57291666666667)"/><use xlink:href="#k" transform="matrix(1,0,0,1,382.8125,445) translate(115.19759259259258 76.57291666666667)"/><use xlink:href="#l" transform="matrix(1,0,0,1,382.8125,445) translate(169.94203703703704 76.57291666666667)"/><use xlink:href="#u" transform="matrix(1,0,0,1,382.8125,445) translate(80.46425925925925 97.37291666666668)"/><path d="M601.5 291.15h-78.74l-2.73.35-2.4 1-2.06 1.6-1.6 2.06-1 2.4-.35 2.72v116.4h-4V301.02l.46-3.5 1.4-3.37 2.23-2.92 2.92-2.23 3.37-1.4 3.5-.45h79zm143.45 0h-107.9v-4h107.9z" fill="#333"/><path d="M746.85 290l.45 1.15h-2.4v-4h2.27zM510.63 433.95L506 419.68h9.26z" fill="#333"/><path d="M510.63 440.42l-7.4-22.74h14.78zm-1.9-18.74l1.9 5.8 1.88-5.8z" fill="#333"/><use xlink:href="#v" transform="matrix(1,0,0,1,601.4944196591581,278.4793852781831) translate(0 14.222222222222223)"/><path d="M512.63 1316.14h-4V645.7h4zm0-689.12h-4V600.2h4z" fill="#333"/><path d="M510.9 598.22l1.73-.25v2.27h-4v-2.28zM510.63 1332.4l-4.64-14.26h9.26z" fill="#333"/><path d="M510.63 1338.88l-7.4-22.74h14.78zm-1.9-18.74l1.9 5.8 1.88-5.8z" fill="#333"/><use xlink:href="#w" transform="matrix(1,0,0,1,495.06944444444446,627.0203558292599) translate(0 12.444444444444446)"/><path d="M853.4 444.54c3.65-2.5 9.55-2.5 13.2 0l101.8 70.22c3.65 2.5 3.65 6.58 0 9.08l-101.8 70.23c-3.65 2.5-9.55 2.5-13.2 0l-101.8-70.23c-3.65-2.5-3.65-6.57 0-9.08z" stroke="#333" stroke-width="4" fill="#83bbe5"/><use xlink:href="#e" transform="matrix(1,0,0,1,750,445) translate(25.480925925925913 69.74166666666667)"/><use xlink:href="#f" transform="matrix(1,0,0,1,750,445) translate(72.56981481481482 69.74166666666667)"/><use xlink:href="#g" transform="matrix(1,0,0,1,750,445) translate(103.28833333333334 69.74166666666667)"/><use xlink:href="#x" transform="matrix(1,0,0,1,750,445) translate(133.09203703703704 69.74166666666667)"/><use xlink:href="#j" transform="matrix(1,0,0,1,750,445) translate(71.58277777777778 90.54166666666669)"/><use xlink:href="#y" transform="matrix(1,0,0,1,750,445) translate(86.02722222222222 90.54166666666669)"/><path d="M1307.5 1340.88c36.93 0 66.88 23.33 66.88 52.1 0 28.77-29.95 52.1-66.88 52.1h-200.63c-36.93 0-66.87-23.33-66.87-52.1 0-28.77 29.94-52.1 66.88-52.1z" stroke="#333" stroke-width="4" fill="#ffc374"/><use xlink:href="#z" transform="matrix(1,0,0,1,1048,1348.8800050763043) translate(10.240740740740705 47.24166666666667)"/><use xlink:href="#A" transform="matrix(1,0,0,1,1048,1348.8800050763043) translate(68.88518518518515 47.24166666666667)"/><use xlink:href="#B" transform="matrix(1,0,0,1,1048,1348.8800050763043) translate(83.32962962962961 47.24166666666667)"/><use xlink:href="#k" transform="matrix(1,0,0,1,1048,1348.8800050763043) translate(121.79999999999998 47.24166666666667)"/><use xlink:href="#p" transform="matrix(1,0,0,1,1048,1348.8800050763043) translate(176.54444444444442 47.24166666666667)"/><use xlink:href="#q" transform="matrix(1,0,0,1,1048,1348.8800050763043) translate(225.22222222222223 47.24166666666667)"/><path d="M862 648.17h-4v-3.12h4zm0-24.45h-4v-23.87h4z" fill="#333"/><path d="M861.24 597.9l.76-.15v2.15h-4v-2.15zM860 664.44l-4.64-14.27h9.28z" fill="#333"/><path d="M860 670.9l-7.4-22.73h14.8zm-1.88-18.73l1.88 5.8 1.88-5.8z" fill="#333"/><use xlink:href="#v" transform="matrix(1,0,0,1,842.2222222222222,623.7153071685252) translate(0.004999999999999005 14.222222222222223)"/><path d="M680.9 521.3h-37.44v-4h37.44zm43.3 0h-16.7v-4h16.7z" fill="#333"/><path d="M643.5 521.3h-2.6l.6-2.17-.63-1.83h2.64zM740.48 519.3l-14.27 4.64v-9.27z" fill="#333"/><path d="M746.95 519.3l-22.74 7.4V511.9zm-18.74 1.9l5.8-1.9-5.8-1.88z" fill="#333"/><use xlink:href="#C" transform="matrix(1,0,0,1,680.8965567379006,508.6366666667249) translate(0.005000000000000782 14.222222222222223)"/><path d="M853.4 674.7c3.65-2.5 9.55-2.5 13.2 0l101.8 70.22c3.65 2.5 3.65 6.57 0 9.08l-101.8 70.22c-3.65 2.5-9.55 2.5-13.2 0L751.6 754c-3.65-2.5-3.65-6.57 0-9.08z" stroke="#333" stroke-width="4" fill="#83bbe5"/><use xlink:href="#e" transform="matrix(1,0,0,1,750,675.1572813885418) translate(73.5087037037037 46.46041666666667)"/><use xlink:href="#D" transform="matrix(1,0,0,1,750,675.1572813885418) translate(120.59759259259259 46.46041666666667)"/><use xlink:href="#E" transform="matrix(1,0,0,1,750,675.1572813885418) translate(38.89018518518519 67.26041666666667)"/><use xlink:href="#F" transform="matrix(1,0,0,1,750,675.1572813885418) translate(95.56055555555557 67.26041666666667)"/><use xlink:href="#i" transform="matrix(1,0,0,1,750,675.1572813885418) translate(115.73462962962964 67.26041666666667)"/><use xlink:href="#G" transform="matrix(1,0,0,1,750,675.1572813885418) translate(171.4901851851852 67.26041666666667)"/><use xlink:href="#H" transform="matrix(1,0,0,1,750,675.1572813885418) translate(35.20685185185184 88.06041666666668)"/><use xlink:href="#p" transform="matrix(1,0,0,1,750,675.1572813885418) translate(98.6661111111111 88.06041666666668)"/><use xlink:href="#I" transform="matrix(1,0,0,1,750,675.1572813885418) translate(147.3438888888889 88.06041666666668)"/><use xlink:href="#m" transform="matrix(1,0,0,1,750,675.1572813885418) translate(83.11425925925926 108.86041666666668)"/><path d="M853.28 904.65c3.7-2.4 9.73-2.4 13.44 0l116.5 75.08c3.73 2.4 3.73 6.27 0 8.66l-116.5 75.07c-3.7 2.4-9.73 2.4-13.44 0l-116.5-75.08c-3.73-2.4-3.73-6.28 0-8.67z" stroke="#333" stroke-width="4" fill="#83bbe5"/><g><use xlink:href="#e" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(60.17351851851851 53.36666666666667)"/><use xlink:href="#f" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(107.26240740740741 53.36666666666667)"/><use xlink:href="#J" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(137.98092592592593 53.36666666666667)"/><use xlink:href="#F" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(45.27166666666666 74.16666666666667)"/><use xlink:href="#i" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(65.44574074074073 74.16666666666667)"/><use xlink:href="#K" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(121.2012962962963 74.16666666666667)"/><use xlink:href="#L" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(151.91981481481483 74.16666666666667)"/><use xlink:href="#t" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(174.01981481481482 74.16666666666667)"/><use xlink:href="#G" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(195.10870370370372 74.16666666666667)"/><use xlink:href="#k" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(75.70129629629629 94.96666666666668)"/><use xlink:href="#l" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(130.44574074074075 94.96666666666668)"/><use xlink:href="#u" transform="matrix(1,0,0,1,735.0416946833639,905.3145627770837) translate(77.96425925925924 115.76666666666668)"/></g><path d="M862 878.18h-4v-4.05h4zm0-25.38h-4V828h4z" fill="#333"/><path d="M862 828.06h-4V826h4zM860 894.45l-4.64-14.27h9.28z" fill="#333"/><path d="M860 900.92l-7.4-22.74h14.8zm-1.88-18.74l1.88 5.8 1.88-5.8z" fill="#333"/><g><use xlink:href="#M" transform="matrix(1,0,0,1,848.1975308641976,852.7975992349) translate(0.005000000000000782 14.222222222222223)"/></g><path d="M853.28 1149.4c3.7-2.38 9.73-2.38 13.44 0l116.5 75.1c3.73 2.4 3.73 6.27 0 8.66l-116.5 75.08c-3.7 2.4-9.73 2.4-13.44 0l-116.5-75.08c-3.73-2.4-3.73-6.27 0-8.67z" stroke="#333" stroke-width="4" fill="#83bbe5"/><g><use xlink:href="#e" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(60.17351851851851 53.36666666666667)"/><use xlink:href="#f" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(107.26240740740741 53.36666666666667)"/><use xlink:href="#J" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(137.98092592592593 53.36666666666667)"/><use xlink:href="#N" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(33.186481481481465 74.16666666666667)"/><use xlink:href="#i" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(85.13833333333332 74.16666666666667)"/><use xlink:href="#K" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(140.89388888888888 74.16666666666667)"/><use xlink:href="#O" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(171.6124074074074 74.16666666666667)"/><use xlink:href="#t" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(57.934629629629626 94.96666666666668)"/><use xlink:href="#j" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(79.02351851851851 94.96666666666668)"/><use xlink:href="#k" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(93.46796296296296 94.96666666666668)"/><use xlink:href="#l" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(148.2124074074074 94.96666666666668)"/><use xlink:href="#u" transform="matrix(1,0,0,1,735.0416946833639,1150.080005076304) translate(77.96425925925924 115.76666666666668)"/></g><path d="M1198.82 1227.28l3.37 1.4 2.9 2.23 2.23 2.93 1.4 3.36.46 3.5v75.44h-4v-75.18l-.37-2.73-1-2.4-1.58-2.06-2.08-1.6-2.4-1-2.7-.34h-148.2v-4h148.45zm-208.85 3.55v-4h30.3v4z" fill="#333"/><path d="M990.02 1230.83h-2.42l.47-2.55-.55-1.45h2.5zM1207.2 1332.4l-4.65-14.26h9.27z" fill="#333"/><path d="M1207.2 1338.88l-7.4-22.74h14.78zm-1.9-18.74l1.9 5.8 1.87-5.8z" fill="#333"/><g><use xlink:href="#P" transform="matrix(1,0,0,1,1020.2660053308865,1218.1587489130575) translate(0.005000000000000782 14.222222222222223)"/></g><path d="M673.72 751.46H522.76l-2.73.36-2.4 1-2.06 1.58-1.6 2.08-1 2.4-.35 2.7v554.56h-4v-554.8l.46-3.5 1.4-3.38 2.23-2.9 2.92-2.25 3.37-1.38 3.5-.46H673.7zm71.23 0h-35.67v-4h35.67z" fill="#333"/><path d="M746.85 750.3l.45 1.16h-2.4v-4h2.27zM510.63 1332.4l-4.64-14.26h9.26z" fill="#333"/><path d="M510.63 1338.88l-7.4-22.74h14.78zm-1.9-18.74l1.9 5.8 1.88-5.8z" fill="#333"/><g><use xlink:href="#v" transform="matrix(1,0,0,1,673.7209768070078,738.7939479142002) translate(0.004999999999999005 14.222222222222223)"/></g><path d="M661.98 986.06H522.76l-2.73.36-2.4 1-2.06 1.58-1.6 2.08-1 2.4-.35 2.7v319.96h-4v-320.2l.46-3.52 1.4-3.36 2.23-2.9 2.92-2.25 3.37-1.38 3.5-.46h139.48zm68.05 0h-32.5v-4h32.5z" fill="#333"/><path d="M731.93 984.6l.55 1.46h-2.5v-4h2.42zM510.63 1332.4l-4.64-14.26h9.26z" fill="#333"/><path d="M510.63 1338.88l-7.4-22.74h14.78zm-1.9-18.74l1.9 5.8 1.88-5.8z" fill="#333"/><g><use xlink:href="#v" transform="matrix(1,0,0,1,661.9751622958878,973.3933066138372) translate(0.004999999999999005 14.222222222222223)"/></g><path d="M862 1122.95h-4v-5.84h4zm0-27.17h-4v-26.58h4z" fill="#333"/><path d="M860.82 1067.26l1.18-.2v2.2h-4v-2.22zM860 1139.22l-4.64-14.27h9.28z" fill="#333"/><path d="M860 1145.7l-7.4-22.75h14.8zm-1.88-18.75l1.88 5.8 1.88-5.8z" fill="#333"/><g><use xlink:href="#C" transform="matrix(1,0,0,1,846.6913581165591,1095.7760277587056) translate(0.005000000000000782 14.222222222222223)"/></g><path d="M660 1230.83H522.76l-2.73.35-2.4 1-2.06 1.6-1.6 2.06-1 2.4-.35 2.72v75.18h-4v-75.45l.46-3.5 1.4-3.37 2.23-2.92 2.92-2.22 3.37-1.4 3.5-.45H660zm70.03 0h-34.48v-4h34.48z" fill="#333"/><path d="M731.93 1229.37l.55 1.46h-2.5v-4h2.42zM510.63 1332.4l-4.64-14.26h9.26z" fill="#333"/><path d="M510.63 1338.88l-7.4-22.74h14.78zm-1.9-18.74l1.9 5.8 1.88-5.8z" fill="#333"/><g><use xlink:href="#v" transform="matrix(1,0,0,1,659.9987071330561,1218.1587489130575) translate(0.004999999999999005 14.222222222222223)"/></g><path d="M1209.2 1316.14h-4V822.7h4zm-10.38-798.38l3.37 1.4 2.9 2.23 2.23 2.9 1.4 3.37.46 3.5v270.2h-4V531.42l-.37-2.72-1-2.38-1.58-2.08-2.08-1.6-2.4-.98-2.7-.36H975.04v-4h220.27z" fill="#333"/><path d="M975.1 521.3h-2.27l.32-2.85-.45-1.15h2.4zM1207.2 1332.4l-4.65-14.26h9.27z" fill="#333"/><path d="M1207.2 1338.88l-7.4-22.74h14.78zm-1.9-18.74l1.9 5.8 1.87-5.8z" fill="#333"/><g><use xlink:href="#Q" transform="matrix(1,0,0,1,1195.3850308641975,801.3561875753189) translate(0.005000000000000782 14.222222222222223)"/></g><defs><path fill="#333" d="M24 0v-248h52V0H24" id="R"/><path fill="#333" d="M220-157c-53 9-28 100-34 157h-49v-107c1-27-5-49-29-50C55-147 81-57 75 0H25l-1-190h47c2 12-1 28 3 38 10-53 101-56 108 0 13-22 24-43 59-42 82 1 51 116 57 194h-49v-107c-1-25-5-48-29-50" id="S"/><path fill="#333" d="M135-194c53 0 70 44 70 98 0 56-19 98-73 100-31 1-45-17-59-34 3 33 2 69 2 105H25l-1-265h48c2 10 0 23 3 31 11-24 29-35 60-35zM114-30c33 0 39-31 40-66 0-38-9-64-40-64-56 0-55 130 0 130" id="T"/><path fill="#333" d="M110-194c64 0 96 36 96 99 0 64-35 99-97 99-61 0-95-36-95-99 0-62 34-99 96-99zm-1 164c35 0 45-28 45-65 0-40-10-65-43-65-34 0-45 26-45 65 0 36 10 65 43 65" id="U"/><path fill="#333" d="M135-150c-39-12-60 13-60 57V0H25l-1-190h47c2 13-1 29 3 40 6-28 27-53 61-41v41" id="V"/><path fill="#333" d="M115-3C79 11 28 4 28-45v-112H4v-33h27l15-45h31v45h36v33H77v99c-1 23 16 31 38 25v30" id="W"/><g id="a"><use transform="matrix(0.05,0,0,0.05,0,0)" xlink:href="#R"/><use transform="matrix(0.05,0,0,0.05,5,0)" xlink:href="#S"/><use transform="matrix(0.05,0,0,0.05,21,0)" xlink:href="#T"/><use transform="matrix(0.05,0,0,0.05,31.950000000000003,0)" xlink:href="#U"/><use transform="matrix(0.05,0,0,0.05,42.900000000000006,0)" xlink:href="#V"/><use transform="matrix(0.05,0,0,0.05,49.900000000000006,0)" xlink:href="#W"/></g><path fill="#333" d="M182 0l-62-99L58 0H3l86-130-79-118h55l55 88 55-88h55l-75 118L237 0h-55" id="X"/><path fill="#333" d="M24 0v-248h52v208h133V0H24" id="Y"/><path fill="#333" d="M76-208v77h127v40H76V0H24v-248h183v40H76" id="Z"/><g id="b"><use transform="matrix(0.05,0,0,0.05,0,0)" xlink:href="#X"/><use transform="matrix(0.05,0,0,0.05,12,0)" xlink:href="#Y"/><use transform="matrix(0.05,0,0,0.05,22.950000000000003,0)" xlink:href="#R"/><use transform="matrix(0.05,0,0,0.05,27.950000000000003,0)" xlink:href="#Z"/><use transform="matrix(0.05,0,0,0.05,38.900000000000006,0)" xlink:href="#Z"/></g><path fill="#333" d="M121-226c-27-7-43 5-38 36h38v33H83V0H34v-157H6v-33h28c-9-59 32-81 87-68v32" id="aa"/><path fill="#333" d="M25-224v-37h50v37H25zM25 0v-190h50V0H25" id="ab"/><path fill="#333" d="M25 0v-261h50V0H25" id="ac"/><path fill="#333" d="M185-48c-13 30-37 53-82 52C43 2 14-33 14-96s30-98 90-98c62 0 83 45 84 108H66c0 31 8 55 39 56 18 0 30-7 34-22zm-45-69c5-46-57-63-70-21-2 6-4 13-4 21h74" id="ad"/><g id="c"><use transform="matrix(0.05,0,0,0.05,0,0)" xlink:href="#aa"/><use transform="matrix(0.05,0,0,0.05,5.95,0)" xlink:href="#ab"/><use transform="matrix(0.05,0,0,0.05,10.95,0)" xlink:href="#ac"/><use transform="matrix(0.05,0,0,0.05,15.949999999999998,0)" xlink:href="#ad"/></g><path fill="#333" d="M175 0L67-191c6 58 2 128 3 191H24v-248h59L193-55c-6-58-2-129-3-193h46V0h-61" id="ae"/><path fill="#333" d="M140-251c80 0 125 45 125 126S219 4 139 4C58 4 15-44 15-125s44-126 125-126zm-1 214c52 0 73-35 73-88 0-50-21-86-72-86-52 0-73 35-73 86s22 88 72 88" id="af"/><g id="d"><use transform="matrix(0.04320987654320988,0,0,0.04320987654320988,0,0)" xlink:href="#ae"/><use transform="matrix(0.04320987654320988,0,0,0.04320987654320988,11.19135802469136,0)" xlink:href="#af"/></g><path d="M24-248c120-7 223 5 221 122C244-46 201 0 124 0H24v-248zM76-40c74 7 117-18 117-86 0-67-45-88-117-82v168" id="ag"/><path d="M110-194c64 0 96 36 96 99 0 64-35 99-97 99-61 0-95-36-95-99 0-62 34-99 96-99zm-1 164c35 0 45-28 45-65 0-40-10-65-43-65-34 0-45 26-45 65 0 36 10 65 43 65" id="ah"/><path d="M185-48c-13 30-37 53-82 52C43 2 14-33 14-96s30-98 90-98c62 0 83 45 84 108H66c0 31 8 55 39 56 18 0 30-7 34-22zm-45-69c5-46-57-63-70-21-2 6-4 13-4 21h74" id="ai"/><path d="M137-138c1-29-70-34-71-4 15 46 118 7 119 86 1 83-164 76-172 9l43-7c4 19 20 25 44 25 33 8 57-30 24-41C81-84 22-81 20-136c-2-80 154-74 161-7" id="aj"/><g id="e"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ag"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,12.470370370370372,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,23.01481481481482,0)" xlink:href="#ai"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,32.64444444444445,0)" xlink:href="#aj"/></g><path d="M115-3C79 11 28 4 28-45v-112H4v-33h27l15-45h31v45h36v33H77v99c-1 23 16 31 38 25v30" id="ak"/><path d="M114-157C55-157 80-60 75 0H25v-261h50l-1 109c12-26 28-41 61-42 86-1 58 113 63 194h-50c-7-57 23-157-34-157" id="al"/><g id="f"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,5.72962962962963,0)" xlink:href="#al"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,16.274074074074075,0)" xlink:href="#ai"/></g><path d="M121-226c-27-7-43 5-38 36h38v33H83V0H34v-157H6v-33h28c-9-59 32-81 87-68v32" id="am"/><path d="M25-224v-37h50v37H25zM25 0v-190h50V0H25" id="an"/><path d="M25 0v-261h50V0H25" id="ao"/><g id="g"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#am"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,5.72962962962963,0)" xlink:href="#an"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,10.544444444444446,0)" xlink:href="#ao"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,15.359259259259261,0)" xlink:href="#ai"/></g><path d="M24 0v-248h52V0H24" id="ap"/><g id="h"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ap"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,4.814814814814816,0)" xlink:href="#ag"/></g><path d="M220-157c-53 9-28 100-34 157h-49v-107c1-27-5-49-29-50C55-147 81-57 75 0H25l-1-190h47c2 12-1 28 3 38 10-53 101-56 108 0 13-22 24-43 59-42 82 1 51 116 57 194h-49v-107c-1-25-5-48-29-50" id="aq"/><path d="M133-34C117-15 103 5 69 4 32 3 11-16 11-54c-1-60 55-63 116-61 1-26-3-47-28-47-18 1-26 9-28 27l-52-2c7-38 36-58 82-57s74 22 75 68l1 82c-1 14 12 18 25 15v27c-30 8-71 5-69-32zm-48 3c29 0 43-24 42-57-32 0-66-3-65 30 0 17 8 27 23 27" id="ar"/><path d="M190-63c-7 42-38 67-86 67-59 0-84-38-90-98-12-110 154-137 174-36l-49 2c-2-19-15-32-35-32-30 0-35 28-38 64-6 74 65 87 74 30" id="as"/><g id="i"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#aq"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,15.40740740740741,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,25.03703703703704,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,30.766666666666673,0)" xlink:href="#as"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,40.396296296296306,0)" xlink:href="#al"/></g><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ar" id="j"/><path d="M169-182c-1-43-94-46-97-3 18 66 151 10 154 114 3 95-165 93-204 36-6-8-10-19-12-30l50-8c3 46 112 56 116 5-17-69-150-10-154-114-4-87 153-88 188-35 5 8 8 18 10 28" id="at"/><path d="M135-150c-39-12-60 13-60 57V0H25l-1-190h47c2 13-1 29 3 40 6-28 27-53 61-41v41" id="au"/><path d="M135-194c87-1 58 113 63 194h-50c-7-57 23-157-34-157-59 0-34 97-39 157H25l-1-190h47c2 12-1 28 3 38 12-26 28-41 61-42" id="av"/><path d="M195-6C206 82 75 100 31 46c-4-6-6-13-8-21l49-6c3 16 16 24 34 25 40 0 42-37 40-79-11 22-30 35-61 35-53 0-70-43-70-97 0-56 18-96 73-97 30 0 46 14 59 34l2-30h47zm-90-29c32 0 41-27 41-63 0-35-9-62-40-62-32 0-39 29-40 63 0 36 9 62 39 62" id="aw"/><g id="k"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#at"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,11.555555555555557,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,17.28518518518519,0)" xlink:href="#au"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,24.02592592592593,0)" xlink:href="#an"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,28.840740740740745,0)" xlink:href="#av"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,39.38518518518519,0)" xlink:href="#aw"/></g><path d="M136-208V0H84v-208H4v-40h212v40h-80" id="ax"/><path d="M135-194c52 0 70 43 70 98 0 56-19 99-73 100-30 1-46-15-58-35L72 0H24l1-261h50v104c11-23 29-37 60-37zM114-30c31 0 40-27 40-66 0-37-7-63-39-63s-41 28-41 65c0 36 8 64 40 64" id="ay"/><g id="l"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ax"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,9.244444444444445,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,18.874074074074077,0)" xlink:href="#ay"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,29.41851851851852,0)" xlink:href="#ao"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,34.23333333333334,0)" xlink:href="#ai"/></g><path d="M67-125c0 54 23 88 75 88 28 0 53-7 68-21v-34h-60v-39h108v91C232-14 192 4 140 4 58 4 20-42 15-125 8-236 126-280 215-234c19 10 29 26 37 47l-47 15c-11-23-29-39-63-39-53 1-75 33-75 86" id="az"/><path d="M238-95c0 69-44 99-111 99C63 4 22-25 22-93v-155h51v151c-1 38 19 59 55 60 90 1 49-130 58-211h52v153" id="aA"/><path d="M110-251c83-7 118 89 53 130-17 10-36 21-38 46H78c2-56 65-53 71-103 2-21-15-35-38-34-25 1-41 14-44 38l-50-2c6-48 39-70 93-75zM77 0v-47h51V0H77" id="aB"/><g id="m"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#az"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,13.481481481481483,0)" xlink:href="#aA"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,25.951851851851856,0)" xlink:href="#ap"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,30.766666666666673,0)" xlink:href="#ag"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,43.23703703703704,0)" xlink:href="#aB"/></g><g id="n"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#aA"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,12.470370370370372,0)" xlink:href="#aj"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,22.1,0)" xlink:href="#ai"/></g><path d="M144 0l-44-69L55 0H2l70-98-66-92h53l41 62 40-62h54l-67 91 71 99h-54" id="aC"/><g id="o"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ai"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,9.629629629629632,0)" xlink:href="#aC"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,19.259259259259263,0)" xlink:href="#an"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,24.07407407407408,0)" xlink:href="#aj"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,33.70370370370371,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,39.43333333333334,0)" xlink:href="#an"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,44.248148148148154,0)" xlink:href="#av"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,54.7925925925926,0)" xlink:href="#aw"/></g><g id="p"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ax"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,9.244444444444445,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,18.874074074074077,0)" xlink:href="#ay"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,29.41851851851852,0)" xlink:href="#ao"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,34.23333333333334,0)" xlink:href="#ai"/></g><path d="M67-125c0 53 21 87 73 88 37 1 54-22 65-47l45 17C233-25 199 4 140 4 58 4 20-42 15-125 8-235 124-281 211-232c18 10 29 29 36 50l-46 12c-8-25-30-41-62-41-52 0-71 34-72 86" id="aD"/><g id="q"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#aD"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,12.470370370370372,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,23.01481481481482,0)" xlink:href="#ao"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,27.829629629629633,0)" xlink:href="#ao"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,32.64444444444445,0)" xlink:href="#ai"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,42.274074074074086,0)" xlink:href="#as"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,51.90370370370372,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,57.63333333333334,0)" xlink:href="#an"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,62.44814814814817,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,72.99259259259262,0)" xlink:href="#av"/></g><g id="r"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ap"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,4.814814814814816,0)" xlink:href="#aj"/></g><path d="M135-194c53 0 70 44 70 98 0 56-19 98-73 100-31 1-45-17-59-34 3 33 2 69 2 105H25l-1-265h48c2 10 0 23 3 31 11-24 29-35 60-35zM114-30c33 0 39-31 40-66 0-38-9-64-40-64-56 0-55 130 0 130" id="aE"/><g id="s"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#aE"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,10.544444444444446,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,20.174074074074078,0)" xlink:href="#au"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,26.914814814814818,0)" xlink:href="#ak"/></g><g id="t"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,10.544444444444446,0)" xlink:href="#am"/></g><g id="u"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#aD"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,12.470370370370372,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,23.01481481481482,0)" xlink:href="#ao"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,27.829629629629633,0)" xlink:href="#ao"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,32.64444444444445,0)" xlink:href="#ai"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,42.274074074074086,0)" xlink:href="#as"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,51.90370370370372,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,57.63333333333334,0)" xlink:href="#an"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,62.44814814814817,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,72.99259259259262,0)" xlink:href="#av"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,83.53703703703707,0)" xlink:href="#aB"/></g><path fill="#333" d="M146-102V0H94v-102L6-248h54l60 105 60-105h54" id="aF"/><path fill="#333" d="M24 0v-248h195v40H76v63h132v40H76v65h150V0H24" id="aG"/><path fill="#333" d="M169-182c-1-43-94-46-97-3 18 66 151 10 154 114 3 95-165 93-204 36-6-8-10-19-12-30l50-8c3 46 112 56 116 5-17-69-150-10-154-114-4-87 153-88 188-35 5 8 8 18 10 28" id="aH"/><g id="v"><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,0,0)" xlink:href="#aF"/><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,11.851851851851853,0)" xlink:href="#aG"/><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,23.703703703703706,0)" xlink:href="#aH"/></g><g id="w"><use transform="matrix(0.04320987654320988,0,0,0.04320987654320988,0,0)" xlink:href="#aF"/><use transform="matrix(0.04320987654320988,0,0,0.04320987654320988,10.370370370370372,0)" xlink:href="#aG"/><use transform="matrix(0.04320987654320988,0,0,0.04320987654320988,20.740740740740744,0)" xlink:href="#aH"/></g><g id="x"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#as"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,9.629629629629632,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,20.174074074074078,0)" xlink:href="#av"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,30.718518518518522,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,36.44814814814815,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,46.07777777777778,0)" xlink:href="#an"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,50.8925925925926,0)" xlink:href="#av"/></g><path d="M85 4C-2 5 27-109 22-190h50c7 57-23 150 33 157 60-5 35-97 40-157h50l1 190h-47c-2-12 1-28-3-38-12 25-28 42-61 42" id="aI"/><g id="y"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#az"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,13.481481481481483,0)" xlink:href="#au"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,20.222222222222225,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,30.766666666666673,0)" xlink:href="#aI"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,41.31111111111112,0)" xlink:href="#aE"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,51.85555555555556,0)" xlink:href="#aB"/></g><g id="z"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#aD"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,12.470370370370372,0)" xlink:href="#au"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,19.211111111111112,0)" xlink:href="#ai"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,28.84074074074074,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,38.470370370370375,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,44.2,0)" xlink:href="#ai"/></g><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ar" id="A"/><path d="M231 0h-52l-39-155L100 0H48L-1-190h46L77-45c9-52 24-97 36-145h53l37 145 32-145h46" id="aJ"/><g id="B"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#av"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,10.544444444444446,0)" xlink:href="#ai"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,20.174074074074078,0)" xlink:href="#aJ"/></g><g id="C"><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,0,0)" xlink:href="#ae"/><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,12.790123456790125,0)" xlink:href="#af"/></g><g id="D"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,5.72962962962963,0)" xlink:href="#al"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,16.274074074074075,0)" xlink:href="#ai"/></g><g id="E"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#az"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,13.481481481481483,0)" xlink:href="#au"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,20.222222222222225,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,30.766666666666673,0)" xlink:href="#aI"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,41.31111111111112,0)" xlink:href="#aE"/></g><path d="M88-194c31-1 46 15 58 34l-1-101h50l1 261h-48c-2-10 0-23-3-31C134-8 116 4 84 4 32 4 16-41 15-95c0-56 19-97 73-99zm17 164c33 0 40-30 41-66 1-37-9-64-41-64s-38 30-39 65c0 43 13 65 39 65" id="aK"/><g id="F"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ap"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,4.814814814814816,0)" xlink:href="#aK"/></g><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ar" id="G"/><g id="H"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#at"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,11.555555555555557,0)" xlink:href="#al"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,22.1,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,31.729629629629635,0)" xlink:href="#au"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,38.470370370370375,0)" xlink:href="#ai"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,48.10000000000001,0)" xlink:href="#aK"/></g><g id="I"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ag"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,12.470370370370372,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,22.1,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,27.829629629629633,0)" xlink:href="#ar"/></g><g id="J"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#az"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,13.481481481481483,0)" xlink:href="#au"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,20.222222222222225,0)" xlink:href="#ah"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,30.766666666666673,0)" xlink:href="#aI"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,41.31111111111112,0)" xlink:href="#aE"/></g><g id="K"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ak"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,5.72962962962963,0)" xlink:href="#al"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,16.274074074074075,0)" xlink:href="#ai"/></g><g id="L"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#ap"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,4.814814814814816,0)" xlink:href="#ag"/></g><g id="M"><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,0,0)" xlink:href="#ae"/><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,12.790123456790125,0)" xlink:href="#U"/></g><path d="M175 0L67-191c6 58 2 128 3 191H24v-248h59L193-55c-6-58-2-129-3-193h46V0h-61" id="aL"/><g id="N"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#aL"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,12.470370370370372,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,22.1,0)" xlink:href="#aq"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,37.50740740740741,0)" xlink:href="#ai"/></g><g id="O"><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,0,0)" xlink:href="#av"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,10.544444444444446,0)" xlink:href="#ar"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,20.174074074074078,0)" xlink:href="#aq"/><use transform="matrix(0.048148148148148155,0,0,0.048148148148148155,35.58148148148149,0)" xlink:href="#ai"/></g><g id="P"><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,0,0)" xlink:href="#ae"/><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,12.790123456790125,0)" xlink:href="#af"/></g><g id="Q"><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,0,0)" xlink:href="#ae"/><use transform="matrix(0.04938271604938272,0,0,0.04938271604938272,12.790123456790125,0)" xlink:href="#U"/></g></defs></g></svg>