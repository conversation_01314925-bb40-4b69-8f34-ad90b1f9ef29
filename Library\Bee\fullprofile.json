{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 13804, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 13804, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 13804, "tid": 164895, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 13804, "tid": 164895, "ts": 1755628622834779, "dur": 1001, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 13804, "tid": 164895, "ts": 1755628622841617, "dur": 1877, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 13804, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 13804, "tid": 1, "ts": 1755628621601318, "dur": 8196, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 13804, "tid": 1, "ts": 1755628621609518, "dur": 123102, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 13804, "tid": 1, "ts": 1755628621732629, "dur": 53233, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 13804, "tid": 164895, "ts": 1755628622843501, "dur": 18, "ph": "X", "name": "", "args": {}}, {"pid": 13804, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621599587, "dur": 8490, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621608080, "dur": 1217219, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621609217, "dur": 2523, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621611745, "dur": 1595, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613362, "dur": 360, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613725, "dur": 11, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613738, "dur": 78, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613906, "dur": 1, "ph": "X", "name": "ProcessMessages 1831", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613908, "dur": 25, "ph": "X", "name": "ReadAsync 1831", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613934, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613936, "dur": 19, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613958, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613960, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621613981, "dur": 59, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614042, "dur": 135, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614182, "dur": 2, "ph": "X", "name": "ProcessMessages 1902", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614185, "dur": 24, "ph": "X", "name": "ReadAsync 1902", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614211, "dur": 1, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614212, "dur": 38, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614273, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614296, "dur": 1, "ph": "X", "name": "ProcessMessages 1620", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614297, "dur": 15, "ph": "X", "name": "ReadAsync 1620", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614315, "dur": 16, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614333, "dur": 36, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614371, "dur": 43, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614440, "dur": 1, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614442, "dur": 121, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614565, "dur": 91, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614657, "dur": 3, "ph": "X", "name": "ProcessMessages 4363", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614660, "dur": 16, "ph": "X", "name": "ReadAsync 4363", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614679, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614742, "dur": 20, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614763, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614765, "dur": 15, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614782, "dur": 34, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614818, "dur": 29, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614848, "dur": 40, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614890, "dur": 25, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614942, "dur": 1, "ph": "X", "name": "ProcessMessages 1405", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614944, "dur": 16, "ph": "X", "name": "ReadAsync 1405", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621614962, "dur": 102, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615091, "dur": 37, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615129, "dur": 2, "ph": "X", "name": "ProcessMessages 3684", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615131, "dur": 38, "ph": "X", "name": "ReadAsync 3684", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615171, "dur": 19, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615229, "dur": 21, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615252, "dur": 1, "ph": "X", "name": "ProcessMessages 1273", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615253, "dur": 39, "ph": "X", "name": "ReadAsync 1273", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615294, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615312, "dur": 14, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615328, "dur": 15, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615345, "dur": 39, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615385, "dur": 158, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615545, "dur": 1, "ph": "X", "name": "ProcessMessages 2113", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615547, "dur": 47, "ph": "X", "name": "ReadAsync 2113", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615595, "dur": 1, "ph": "X", "name": "ProcessMessages 2355", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615597, "dur": 58, "ph": "X", "name": "ReadAsync 2355", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615724, "dur": 1, "ph": "X", "name": "ProcessMessages 1951", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615725, "dur": 28, "ph": "X", "name": "ReadAsync 1951", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615754, "dur": 1, "ph": "X", "name": "ProcessMessages 2053", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615756, "dur": 36, "ph": "X", "name": "ReadAsync 2053", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615794, "dur": 22, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615819, "dur": 33, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615853, "dur": 74, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615929, "dur": 1, "ph": "X", "name": "ProcessMessages 1714", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615930, "dur": 17, "ph": "X", "name": "ReadAsync 1714", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621615972, "dur": 59, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616032, "dur": 1, "ph": "X", "name": "ProcessMessages 1588", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616034, "dur": 16, "ph": "X", "name": "ReadAsync 1588", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616052, "dur": 12, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616065, "dur": 15, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616082, "dur": 25, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616108, "dur": 13, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616123, "dur": 13, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616170, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616192, "dur": 96, "ph": "X", "name": "ReadAsync 1119", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616309, "dur": 1, "ph": "X", "name": "ProcessMessages 1438", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616310, "dur": 37, "ph": "X", "name": "ReadAsync 1438", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616348, "dur": 1, "ph": "X", "name": "ProcessMessages 1448", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616350, "dur": 44, "ph": "X", "name": "ReadAsync 1448", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616395, "dur": 1, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616397, "dur": 13, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616412, "dur": 30, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616444, "dur": 70, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616540, "dur": 1, "ph": "X", "name": "ProcessMessages 1069", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616542, "dur": 18, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616562, "dur": 18, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616582, "dur": 56, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616641, "dur": 1, "ph": "X", "name": "ProcessMessages 1203", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616643, "dur": 217, "ph": "X", "name": "ReadAsync 1203", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616862, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616912, "dur": 27, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621616940, "dur": 119, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617060, "dur": 1, "ph": "X", "name": "ProcessMessages 2297", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617062, "dur": 54, "ph": "X", "name": "ReadAsync 2297", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617143, "dur": 1, "ph": "X", "name": "ProcessMessages 1727", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617146, "dur": 19, "ph": "X", "name": "ReadAsync 1727", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617166, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617167, "dur": 17, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617186, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617236, "dur": 43, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617281, "dur": 14, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617334, "dur": 24, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617360, "dur": 1, "ph": "X", "name": "ProcessMessages 1501", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617361, "dur": 13, "ph": "X", "name": "ReadAsync 1501", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617377, "dur": 139, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617518, "dur": 47, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617566, "dur": 1, "ph": "X", "name": "ProcessMessages 2657", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617568, "dur": 50, "ph": "X", "name": "ReadAsync 2657", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617619, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617620, "dur": 53, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617675, "dur": 1, "ph": "X", "name": "ProcessMessages 1502", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617677, "dur": 113, "ph": "X", "name": "ReadAsync 1502", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617793, "dur": 40, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617834, "dur": 1, "ph": "X", "name": "ProcessMessages 1994", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617836, "dur": 14, "ph": "X", "name": "ReadAsync 1994", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617852, "dur": 13, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617899, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621617945, "dur": 71, "ph": "X", "name": "ReadAsync 1118", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618044, "dur": 1, "ph": "X", "name": "ProcessMessages 1989", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618046, "dur": 40, "ph": "X", "name": "ReadAsync 1989", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618088, "dur": 12, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618180, "dur": 44, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618226, "dur": 1, "ph": "X", "name": "ProcessMessages 2233", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618227, "dur": 17, "ph": "X", "name": "ReadAsync 2233", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618247, "dur": 11, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618261, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618286, "dur": 14, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618302, "dur": 33, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618336, "dur": 19, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618404, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618456, "dur": 1, "ph": "X", "name": "ProcessMessages 2261", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618504, "dur": 75, "ph": "X", "name": "ReadAsync 2261", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618580, "dur": 29, "ph": "X", "name": "ProcessMessages 2391", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618610, "dur": 42, "ph": "X", "name": "ReadAsync 2391", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618653, "dur": 1, "ph": "X", "name": "ProcessMessages 1596", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618654, "dur": 13, "ph": "X", "name": "ReadAsync 1596", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618692, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618715, "dur": 14, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618732, "dur": 14, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621618747, "dur": 34, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621619634, "dur": 185, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621619880, "dur": 10, "ph": "X", "name": "ProcessMessages 20528", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621619926, "dur": 83, "ph": "X", "name": "ReadAsync 20528", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620060, "dur": 2, "ph": "X", "name": "ProcessMessages 3296", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620063, "dur": 65, "ph": "X", "name": "ReadAsync 3296", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620157, "dur": 2, "ph": "X", "name": "ProcessMessages 1747", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620160, "dur": 26, "ph": "X", "name": "ReadAsync 1747", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620188, "dur": 1, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620189, "dur": 15, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620206, "dur": 40, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620247, "dur": 41, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620291, "dur": 16, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620309, "dur": 84, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620395, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620419, "dur": 1, "ph": "X", "name": "ProcessMessages 1578", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620421, "dur": 72, "ph": "X", "name": "ReadAsync 1578", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620495, "dur": 41, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620537, "dur": 1, "ph": "X", "name": "ProcessMessages 1869", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620538, "dur": 18, "ph": "X", "name": "ReadAsync 1869", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620580, "dur": 24, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620605, "dur": 16, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620623, "dur": 14, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620639, "dur": 14, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620655, "dur": 14, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620722, "dur": 26, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620749, "dur": 1, "ph": "X", "name": "ProcessMessages 1606", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620750, "dur": 13, "ph": "X", "name": "ReadAsync 1606", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620785, "dur": 98, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620884, "dur": 1, "ph": "X", "name": "ProcessMessages 2543", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620886, "dur": 33, "ph": "X", "name": "ReadAsync 2543", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620921, "dur": 42, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620964, "dur": 1, "ph": "X", "name": "ProcessMessages 1116", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621620965, "dur": 12, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621003, "dur": 28, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621033, "dur": 85, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621148, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621175, "dur": 33, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621210, "dur": 27, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621239, "dur": 271, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621532, "dur": 1, "ph": "X", "name": "ProcessMessages 1512", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621533, "dur": 34, "ph": "X", "name": "ReadAsync 1512", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621568, "dur": 1, "ph": "X", "name": "ProcessMessages 1838", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621570, "dur": 174, "ph": "X", "name": "ReadAsync 1838", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621745, "dur": 1, "ph": "X", "name": "ProcessMessages 3155", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621785, "dur": 41, "ph": "X", "name": "ReadAsync 3155", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621828, "dur": 17, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621847, "dur": 12, "ph": "X", "name": "ReadAsync 1351", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621621861, "dur": 158, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622023, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622026, "dur": 46, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622074, "dur": 2, "ph": "X", "name": "ProcessMessages 2327", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622077, "dur": 24, "ph": "X", "name": "ReadAsync 2327", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622103, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622105, "dur": 84, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622191, "dur": 1, "ph": "X", "name": "ProcessMessages 1724", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622193, "dur": 41, "ph": "X", "name": "ReadAsync 1724", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622275, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622276, "dur": 49, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622328, "dur": 13, "ph": "X", "name": "ReadAsync 1230", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622343, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622359, "dur": 63, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622423, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622425, "dur": 37, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622464, "dur": 23, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622488, "dur": 1, "ph": "X", "name": "ProcessMessages 1301", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622489, "dur": 128, "ph": "X", "name": "ReadAsync 1301", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622698, "dur": 35, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622733, "dur": 2, "ph": "X", "name": "ProcessMessages 3879", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622736, "dur": 19, "ph": "X", "name": "ReadAsync 3879", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622757, "dur": 14, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622773, "dur": 12, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622787, "dur": 18, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622806, "dur": 58, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622867, "dur": 14, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622909, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622955, "dur": 1, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621622956, "dur": 149, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623109, "dur": 29, "ph": "X", "name": "ProcessMessages 2113", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623140, "dur": 76, "ph": "X", "name": "ReadAsync 2113", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623217, "dur": 1, "ph": "X", "name": "ProcessMessages 1271", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623218, "dur": 74, "ph": "X", "name": "ReadAsync 1271", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623334, "dur": 1, "ph": "X", "name": "ProcessMessages 2152", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623336, "dur": 62, "ph": "X", "name": "ReadAsync 2152", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623400, "dur": 1, "ph": "X", "name": "ProcessMessages 1335", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623401, "dur": 175, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623607, "dur": 39, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623648, "dur": 2, "ph": "X", "name": "ProcessMessages 4256", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623650, "dur": 14, "ph": "X", "name": "ReadAsync 4256", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623666, "dur": 14, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623682, "dur": 76, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623759, "dur": 1, "ph": "X", "name": "ProcessMessages 1481", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623761, "dur": 34, "ph": "X", "name": "ReadAsync 1481", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623820, "dur": 21, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623843, "dur": 36, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623881, "dur": 9, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623892, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623909, "dur": 14, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623949, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621623971, "dur": 13, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624008, "dur": 53, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624063, "dur": 38, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624103, "dur": 50, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624175, "dur": 136, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624313, "dur": 2, "ph": "X", "name": "ProcessMessages 2131", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624316, "dur": 30, "ph": "X", "name": "ReadAsync 2131", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624347, "dur": 1, "ph": "X", "name": "ProcessMessages 2273", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624349, "dur": 126, "ph": "X", "name": "ReadAsync 2273", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624479, "dur": 3, "ph": "X", "name": "ProcessMessages 2073", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624483, "dur": 20, "ph": "X", "name": "ReadAsync 2073", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624506, "dur": 19, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624529, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624550, "dur": 52, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624605, "dur": 67, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624748, "dur": 1, "ph": "X", "name": "ProcessMessages 1349", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624750, "dur": 34, "ph": "X", "name": "ReadAsync 1349", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624812, "dur": 2, "ph": "X", "name": "ProcessMessages 2771", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624815, "dur": 15, "ph": "X", "name": "ReadAsync 2771", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624832, "dur": 57, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624891, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624893, "dur": 35, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624930, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624946, "dur": 43, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621624991, "dur": 41, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625063, "dur": 43, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625108, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625131, "dur": 1, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625132, "dur": 14, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625148, "dur": 115, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625265, "dur": 34, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625300, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625302, "dur": 15, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625318, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625349, "dur": 41, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625391, "dur": 29, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625422, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625440, "dur": 13, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625482, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625507, "dur": 1, "ph": "X", "name": "ProcessMessages 1221", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625509, "dur": 13, "ph": "X", "name": "ReadAsync 1221", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625524, "dur": 13, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625570, "dur": 41, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625612, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625657, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625691, "dur": 31, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625723, "dur": 17, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625765, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625811, "dur": 13, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625826, "dur": 72, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625900, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625922, "dur": 1, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625923, "dur": 13, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621625961, "dur": 48, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626011, "dur": 18, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626031, "dur": 19, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626052, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626149, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626152, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626177, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626178, "dur": 17, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626223, "dur": 37, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626282, "dur": 13, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626298, "dur": 16, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626316, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626362, "dur": 1, "ph": "X", "name": "ProcessMessages 1026", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626363, "dur": 50, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626414, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626450, "dur": 17, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626470, "dur": 48, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626519, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626535, "dur": 55, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626592, "dur": 50, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626662, "dur": 32, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626696, "dur": 16, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626752, "dur": 47, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626800, "dur": 1, "ph": "X", "name": "ProcessMessages 1222", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626802, "dur": 27, "ph": "X", "name": "ReadAsync 1222", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626831, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626872, "dur": 16, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626890, "dur": 47, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626939, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626954, "dur": 15, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626971, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621626987, "dur": 54, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627086, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627109, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627110, "dur": 36, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627173, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627209, "dur": 1, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627210, "dur": 37, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627249, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627283, "dur": 15, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627300, "dur": 74, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627401, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627446, "dur": 1, "ph": "X", "name": "ProcessMessages 1215", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627471, "dur": 16, "ph": "X", "name": "ReadAsync 1215", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627515, "dur": 16, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627533, "dur": 63, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621627599, "dur": 16, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628375, "dur": 101, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628499, "dur": 5, "ph": "X", "name": "ProcessMessages 9188", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628505, "dur": 38, "ph": "X", "name": "ReadAsync 9188", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628544, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628545, "dur": 15, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628562, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628580, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628627, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628629, "dur": 29, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628660, "dur": 30, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628716, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628741, "dur": 1, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628743, "dur": 44, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628789, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628831, "dur": 12, "ph": "X", "name": "ReadAsync 1009", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628863, "dur": 33, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628918, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628940, "dur": 12, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621628976, "dur": 30, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629007, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629023, "dur": 12, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629036, "dur": 129, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629167, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629183, "dur": 12, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629197, "dur": 72, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629274, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629310, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629312, "dur": 93, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629407, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629409, "dur": 29, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629441, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629443, "dur": 16, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629462, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629500, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629517, "dur": 56, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629575, "dur": 13, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629590, "dur": 13, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629605, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629629, "dur": 12, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629643, "dur": 22, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629666, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629681, "dur": 28, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629711, "dur": 12, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629779, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629848, "dur": 31, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629879, "dur": 1, "ph": "X", "name": "ProcessMessages 1889", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629881, "dur": 54, "ph": "X", "name": "ReadAsync 1889", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629937, "dur": 45, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621629986, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630016, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630019, "dur": 68, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630088, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630090, "dur": 36, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630131, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630161, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630163, "dur": 28, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630194, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630196, "dur": 30, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630229, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630231, "dur": 28, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630262, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630264, "dur": 24, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630291, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630293, "dur": 49, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630344, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630346, "dur": 150, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630499, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630501, "dur": 38, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630541, "dur": 2, "ph": "X", "name": "ProcessMessages 2043", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630544, "dur": 23, "ph": "X", "name": "ReadAsync 2043", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630570, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630572, "dur": 34, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630609, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630611, "dur": 23, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630637, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630638, "dur": 52, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630693, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630694, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630722, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630724, "dur": 28, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630755, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630756, "dur": 21, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630780, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630808, "dur": 53, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630863, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630865, "dur": 21, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630888, "dur": 47, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630937, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630953, "dur": 13, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630968, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621630990, "dur": 51, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631043, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631058, "dur": 13, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631073, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631105, "dur": 28, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631135, "dur": 14, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631151, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631166, "dur": 64, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631232, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631256, "dur": 53, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631310, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631327, "dur": 14, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631370, "dur": 14, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631386, "dur": 27, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631414, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631462, "dur": 1, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631464, "dur": 46, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631511, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631526, "dur": 14, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631542, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631564, "dur": 47, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631612, "dur": 13, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631627, "dur": 13, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631642, "dur": 44, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631689, "dur": 52, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631742, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631771, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631795, "dur": 45, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631842, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631858, "dur": 13, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631872, "dur": 15, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631889, "dur": 12, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631903, "dur": 47, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631952, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631968, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621631989, "dur": 13, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632004, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632059, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632123, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632125, "dur": 20, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632147, "dur": 14, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632162, "dur": 14, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632178, "dur": 16, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632196, "dur": 40, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632238, "dur": 21, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632260, "dur": 1, "ph": "X", "name": "ProcessMessages 1173", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632261, "dur": 12, "ph": "X", "name": "ReadAsync 1173", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632275, "dur": 12, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632289, "dur": 37, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632329, "dur": 40, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632371, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632386, "dur": 13, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632401, "dur": 13, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632416, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632432, "dur": 13, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632447, "dur": 13, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632462, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632477, "dur": 40, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632520, "dur": 52, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632574, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632597, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632613, "dur": 12, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632627, "dur": 48, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632677, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632692, "dur": 12, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632706, "dur": 15, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632723, "dur": 13, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632737, "dur": 50, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632789, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632830, "dur": 1, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632832, "dur": 21, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632854, "dur": 49, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621632905, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633008, "dur": 62, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633071, "dur": 1, "ph": "X", "name": "ProcessMessages 2015", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633073, "dur": 26, "ph": "X", "name": "ReadAsync 2015", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633101, "dur": 26, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633129, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633188, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633260, "dur": 1, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633262, "dur": 65, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633329, "dur": 28, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633358, "dur": 12, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633372, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633389, "dur": 13, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633404, "dur": 36, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633441, "dur": 53, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633496, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633537, "dur": 45, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633584, "dur": 21, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633607, "dur": 12, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633620, "dur": 54, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633676, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633697, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633714, "dur": 13, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633729, "dur": 13, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633744, "dur": 13, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633759, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633776, "dur": 14, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633792, "dur": 13, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633807, "dur": 11, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633819, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633839, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633896, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633911, "dur": 13, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633926, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633943, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633958, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621633996, "dur": 17, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634014, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634015, "dur": 13, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634030, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634080, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634096, "dur": 40, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634160, "dur": 22, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634207, "dur": 42, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634252, "dur": 12, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634295, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634353, "dur": 13, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634385, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634413, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634454, "dur": 21, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634478, "dur": 47, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634526, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634584, "dur": 52, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634637, "dur": 1, "ph": "X", "name": "ProcessMessages 2535", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634639, "dur": 32, "ph": "X", "name": "ReadAsync 2535", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634696, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634724, "dur": 16, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634742, "dur": 13, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634756, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634807, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634823, "dur": 60, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634905, "dur": 39, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634946, "dur": 14, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621634962, "dur": 54, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635018, "dur": 19, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635039, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635060, "dur": 13, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635075, "dur": 13, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635112, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635148, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635214, "dur": 1, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635216, "dur": 33, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635269, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635298, "dur": 55, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635417, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635518, "dur": 25, "ph": "X", "name": "ProcessMessages 2257", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635566, "dur": 20, "ph": "X", "name": "ReadAsync 2257", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635587, "dur": 1, "ph": "X", "name": "ProcessMessages 1179", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635605, "dur": 26, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635633, "dur": 27, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635662, "dur": 25, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635688, "dur": 11, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635699, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635745, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635851, "dur": 61, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635913, "dur": 23, "ph": "X", "name": "ProcessMessages 1825", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621635937, "dur": 61, "ph": "X", "name": "ReadAsync 1825", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636083, "dur": 57, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636141, "dur": 1, "ph": "X", "name": "ProcessMessages 2106", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636142, "dur": 91, "ph": "X", "name": "ReadAsync 2106", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636268, "dur": 47, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636341, "dur": 1, "ph": "X", "name": "ProcessMessages 3126", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636370, "dur": 29, "ph": "X", "name": "ReadAsync 3126", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636423, "dur": 68, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636494, "dur": 49, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636639, "dur": 70, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636772, "dur": 1, "ph": "X", "name": "ProcessMessages 1461", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636797, "dur": 36, "ph": "X", "name": "ReadAsync 1461", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636881, "dur": 28, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621636935, "dur": 43, "ph": "X", "name": "ReadAsync 1206", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637026, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637073, "dur": 1, "ph": "X", "name": "ProcessMessages 1213", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637096, "dur": 69, "ph": "X", "name": "ReadAsync 1213", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637167, "dur": 40, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637230, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637269, "dur": 1, "ph": "X", "name": "ProcessMessages 1227", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637296, "dur": 46, "ph": "X", "name": "ReadAsync 1227", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637343, "dur": 1, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637345, "dur": 68, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637437, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637467, "dur": 24, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637493, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637564, "dur": 25, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637617, "dur": 53, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637693, "dur": 45, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637740, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637806, "dur": 45, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637853, "dur": 44, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637898, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637943, "dur": 1, "ph": "X", "name": "ProcessMessages 1234", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621637944, "dur": 55, "ph": "X", "name": "ReadAsync 1234", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638038, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638071, "dur": 1, "ph": "X", "name": "ProcessMessages 1627", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638091, "dur": 45, "ph": "X", "name": "ReadAsync 1627", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638156, "dur": 1, "ph": "X", "name": "ProcessMessages 1071", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638158, "dur": 44, "ph": "X", "name": "ReadAsync 1071", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638204, "dur": 64, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638283, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638284, "dur": 49, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638352, "dur": 1, "ph": "X", "name": "ProcessMessages 1825", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638379, "dur": 50, "ph": "X", "name": "ReadAsync 1825", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638453, "dur": 23, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638476, "dur": 1, "ph": "X", "name": "ProcessMessages 1291", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638478, "dur": 44, "ph": "X", "name": "ReadAsync 1291", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638548, "dur": 19, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638569, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638638, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638682, "dur": 1, "ph": "X", "name": "ProcessMessages 1952", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638703, "dur": 24, "ph": "X", "name": "ReadAsync 1952", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638729, "dur": 23, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638778, "dur": 29, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621638809, "dur": 486, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621639300, "dur": 19, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621639320, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621639446, "dur": 239, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621639703, "dur": 134, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621639849, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621639851, "dur": 82, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621639950, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621639952, "dur": 116, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640085, "dur": 19, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640106, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640187, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640221, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640264, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640356, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640358, "dur": 481, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640846, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640863, "dur": 52, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640935, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640937, "dur": 55, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621640997, "dur": 66, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641066, "dur": 103, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641180, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641181, "dur": 135, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641336, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641360, "dur": 65, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641437, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641439, "dur": 55, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641525, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641644, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641646, "dur": 34, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641686, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641688, "dur": 78, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641833, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641920, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621641922, "dur": 96, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642031, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642052, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642101, "dur": 87, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642200, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642244, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642284, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642352, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642390, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642392, "dur": 65, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642470, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642472, "dur": 76, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642562, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642605, "dur": 93, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642746, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642836, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642840, "dur": 76, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642928, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621642930, "dur": 27, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621643713, "dur": 124, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621643860, "dur": 273, "ph": "X", "name": "ProcessMessages 2551", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621644231, "dur": 339, "ph": "X", "name": "ReadAsync 2551", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621644802, "dur": 6, "ph": "X", "name": "ProcessMessages 1360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621644821, "dur": 66, "ph": "X", "name": "ReadAsync 1360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621644893, "dur": 12, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621644932, "dur": 39, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621644974, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621644976, "dur": 36, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621645021, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621645064, "dur": 209, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621645436, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621645439, "dur": 684, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621646221, "dur": 4, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621646313, "dur": 42, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621646383, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621646433, "dur": 6942, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621653410, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621653536, "dur": 272, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621653862, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621653921, "dur": 189, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621654172, "dur": 58, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621654232, "dur": 172, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621654459, "dur": 52, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621654513, "dur": 26827, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621681364, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621681367, "dur": 165, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621681700, "dur": 30, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621681732, "dur": 59, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621681794, "dur": 1814, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621683617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621683620, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621683686, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621683689, "dur": 219, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621683910, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621684023, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621684074, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621684214, "dur": 61, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621684362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621684366, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621684523, "dur": 148, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621684775, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621684779, "dur": 24948, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621709737, "dur": 826, "ph": "X", "name": "ProcessMessages 4584", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621710569, "dur": 53524, "ph": "X", "name": "ReadAsync 4584", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621764100, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621764104, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621764138, "dur": 1693, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621765836, "dur": 5521, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621771364, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621771367, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621771416, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621771419, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621771545, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621771571, "dur": 1007, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621772583, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621772585, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621772615, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621772655, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621772686, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621772688, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621772717, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621772735, "dur": 403, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773144, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773176, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773203, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773385, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773416, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773506, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773534, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773536, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773677, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621773696, "dur": 503, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774204, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774206, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774242, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774419, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774459, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774461, "dur": 420, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774883, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774885, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774918, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621774921, "dur": 999, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621775925, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621775955, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621775985, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621776119, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621776121, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621776164, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621776165, "dur": 277, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621776446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621776449, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621776483, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621776485, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621776512, "dur": 539, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777054, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777085, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777087, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777114, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777212, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777238, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777240, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777281, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777299, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777636, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777638, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777663, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777863, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777895, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621777898, "dur": 463, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621778363, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621778380, "dur": 373, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621778757, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621778759, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621778792, "dur": 657, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621779455, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621779492, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621779494, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621779521, "dur": 548, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780072, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780113, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780115, "dur": 291, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780408, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780428, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780610, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780649, "dur": 265, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780917, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780953, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780955, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621780982, "dur": 532, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621781516, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621781545, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621781547, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621781761, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621781779, "dur": 480, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621782265, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621782290, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621782310, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621782334, "dur": 764, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621783101, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621783119, "dur": 321, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621783445, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621783469, "dur": 414, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621783885, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621783923, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621783925, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621783957, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621783958, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621784091, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621784121, "dur": 300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621784425, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621784446, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621784532, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621784554, "dur": 394, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621784952, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621784980, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621784982, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621785056, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621785079, "dur": 329, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621785412, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621785413, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621785438, "dur": 631, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621786073, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621786101, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621786102, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621786125, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621786299, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621786318, "dur": 686, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787007, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787036, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787037, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787147, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787167, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787222, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787240, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787473, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787505, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787508, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787541, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621787562, "dur": 1176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621788742, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621788783, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621788786, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621788824, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621788827, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621788854, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621788913, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621788937, "dur": 828, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621789769, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621789771, "dur": 175, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621789951, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621789953, "dur": 576, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621790534, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621790559, "dur": 410, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621790974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621790975, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621791012, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621791014, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621791076, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621791112, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621791114, "dur": 796, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621791913, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621791940, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792065, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792091, "dur": 320, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792414, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792446, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792448, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792481, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792501, "dur": 159, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792664, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792666, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792691, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792857, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792890, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621792893, "dur": 1148, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794045, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794070, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794168, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794191, "dur": 509, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794703, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794729, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794732, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794776, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794797, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794948, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794950, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621794982, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795006, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795035, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795038, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795060, "dur": 225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795288, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795320, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795322, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795355, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795374, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795401, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795403, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795429, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795455, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795491, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795493, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795520, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795552, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795579, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795600, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795622, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795668, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795692, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795712, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795738, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795759, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795803, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795844, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795868, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795887, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795923, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795925, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795955, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621795976, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796005, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796007, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796054, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796056, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796078, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796110, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796112, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796198, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796217, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796244, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796246, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796271, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796306, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796331, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796332, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796355, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796356, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796379, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796406, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796408, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796430, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796451, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796477, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796479, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796505, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796507, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796541, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796543, "dur": 22, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796567, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796570, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796594, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796617, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796643, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796645, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796669, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796691, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796693, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796722, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796724, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796749, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796768, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796793, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796795, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796818, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796837, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796883, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796886, "dur": 20, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796908, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796909, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796931, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796958, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796959, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796987, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621796990, "dur": 30, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797021, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797022, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797054, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797057, "dur": 31, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797090, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797093, "dur": 27, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797122, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797124, "dur": 27, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797154, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797156, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797196, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797198, "dur": 41, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797244, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797246, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797427, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797429, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797471, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797509, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797558, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621797560, "dur": 73587, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621871158, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621871163, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621871198, "dur": 26, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621871226, "dur": 45936, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621917171, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621917175, "dur": 140, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621917321, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628621917324, "dur": 255009, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622172347, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622172353, "dur": 224, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622172592, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622172599, "dur": 103, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622172706, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622172709, "dur": 132101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622304819, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622304823, "dur": 171, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622304995, "dur": 23, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622305019, "dur": 17580, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622322608, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622322612, "dur": 174, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622322790, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622322792, "dur": 2415, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622325219, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622325222, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622325295, "dur": 21, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622325318, "dur": 13544, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622338870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622338873, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622338889, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622338891, "dur": 2979, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622341879, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622341882, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622341902, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622341926, "dur": 373599, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622715532, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622715535, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622715671, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622715674, "dur": 1862, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622717551, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622717558, "dur": 234, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622717803, "dur": 36, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622717841, "dur": 84007, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622801866, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622801873, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622801965, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622801973, "dur": 1446, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622803430, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622803435, "dur": 172, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622803613, "dur": 777, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755628622804399, "dur": 19557, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 13804, "tid": 164895, "ts": 1755628622843522, "dur": 2034, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 13804, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 13804, "tid": 8589934592, "ts": 1755628621595943, "dur": 189951, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 13804, "tid": 8589934592, "ts": 1755628621785896, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 13804, "tid": 8589934592, "ts": 1755628621785903, "dur": 1280, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 13804, "tid": 164895, "ts": 1755628622845558, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 13804, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 13804, "tid": 4294967296, "ts": 1755628621577156, "dur": 1249276, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 13804, "tid": 4294967296, "ts": 1755628621582522, "dur": 6958, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 13804, "tid": 4294967296, "ts": 1755628622826539, "dur": 5236, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 13804, "tid": 4294967296, "ts": 1755628622829773, "dur": 189, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 13804, "tid": 4294967296, "ts": 1755628622831840, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 13804, "tid": 164895, "ts": 1755628622845568, "dur": 38, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755628621605044, "dur": 1908, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755628621606959, "dur": 1174, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755628621608296, "dur": 142, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1755628621608439, "dur": 523, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755628621609133, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B2C9ADF926E837D8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755628621609962, "dur": 920, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_82714E7E5E4D2D26.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755628621611573, "dur": 1949, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755628621619471, "dur": 190, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1755628621625924, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755628621628247, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 0, "ts": 1755628621631038, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755628621631423, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1755628621608991, "dur": 29714, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755628621638727, "dur": 1163091, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755628622801819, "dur": 337, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755628622802157, "dur": 352, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755628622802809, "dur": 170, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755628622803028, "dur": 3297, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1755628621608833, "dur": 29901, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621638760, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621638847, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F904655B297DA2DA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755628621639334, "dur": 1248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621640583, "dur": 774, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F904655B297DA2DA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755628621641753, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1755628621641859, "dur": 659, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1755628621642686, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1755628621642794, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1755628621642865, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621643387, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621643971, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621644328, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621644403, "dur": 688, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1755628621645517, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1651989724058383629.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1755628621645611, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621646343, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621647003, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621647613, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621648435, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621649641, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.localization@7eec7ebf749d\\Editor\\Search\\QueryBuilder\\Blocks\\MetadataValueFilterBlock.cs"}}, {"pid": 12345, "tid": 1, "ts": 1755628621649188, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621650449, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621650885, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621651358, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621651824, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621652270, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621652740, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621653208, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621653731, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621654264, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621654826, "dur": 1533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Flow\\Framework\\Control\\ForAnalyser.cs"}}, {"pid": 12345, "tid": 1, "ts": 1755628621654749, "dur": 2792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621658142, "dur": 795, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\UshortInspector.cs"}}, {"pid": 12345, "tid": 1, "ts": 1755628621657541, "dur": 5503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621663045, "dur": 2856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621665901, "dur": 2208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621668110, "dur": 3270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621671381, "dur": 3670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621675052, "dur": 3386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621678438, "dur": 2791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621681230, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621682148, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621682750, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621683306, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621683518, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755628621683876, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621683957, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621684705, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621684959, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755628621685152, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621685215, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621685896, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621686072, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755628621686397, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621686464, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755628621686760, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621686846, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.013.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755628621687112, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621687839, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621688214, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621688974, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621689160, "dur": 1657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621690818, "dur": 2803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621693674, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621694340, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621694485, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621694617, "dur": 1990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621696608, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755628621696814, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621697198, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621697354, "dur": 71740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621769114, "dur": 3839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Components.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621772954, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621773043, "dur": 3157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621776208, "dur": 5950, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621782164, "dur": 3025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621785190, "dur": 961, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621786161, "dur": 3351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.CompilerSymbols.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621789513, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621789615, "dur": 3641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755628621793257, "dur": 1295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621794565, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621794656, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621794835, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621795344, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621795754, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621796133, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621796337, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621796463, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621796521, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621796810, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621797010, "dur": 77639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628621874737, "dur": 289455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1755628621874652, "dur": 293155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755628622170916, "dur": 462, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755628622173065, "dur": 151963, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755628622338527, "dur": 376762, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1755628622338512, "dur": 376779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1755628622715309, "dur": 2029, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1755628622717348, "dur": 84397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621608943, "dur": 29818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621638772, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_A4F99F59C0C92426.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621638919, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621639111, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_AC4D3319F12A168E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621639233, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621639302, "dur": 1396, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E1660E22076E251.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621640701, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_8029272894F862A3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621641301, "dur": 673, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621642669, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621643450, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1755628621643685, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621644325, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621644409, "dur": 1064, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755628621645542, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621646181, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621646807, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621647435, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621648261, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621649640, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.localization@7eec7ebf749d\\Editor\\UI\\CharacterSet\\ExportCharacterSetWindow.cs"}}, {"pid": 12345, "tid": 2, "ts": 1755628621649006, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621650232, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621650695, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621651158, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621651812, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621652498, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621653152, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621653810, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621654508, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621655230, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621656031, "dur": 910, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Windows\\Sidebars\\Sidebar.cs"}}, {"pid": 12345, "tid": 2, "ts": 1755628621655949, "dur": 2029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621658437, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\GraphEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1755628621657979, "dur": 5086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621663065, "dur": 2861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621665927, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621667770, "dur": 2355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621670125, "dur": 3506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621673642, "dur": 3680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621677322, "dur": 3343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621680665, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621681977, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621682676, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621683408, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621683917, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621684214, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621684284, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621685019, "dur": 2855, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621687886, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621687962, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621688393, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621688456, "dur": 1415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621689871, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621690040, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621690189, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621690265, "dur": 1607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621691873, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621692024, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621692319, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621692934, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621693619, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621694230, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621694488, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621694612, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621694701, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755628621694864, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621695036, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621696293, "dur": 992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621697286, "dur": 71772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621769060, "dur": 1919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621770980, "dur": 1589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621772581, "dur": 4239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621776821, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621776965, "dur": 3688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621780653, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621780780, "dur": 4211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621784998, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621785261, "dur": 3303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621788565, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621788667, "dur": 3762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621792431, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621792738, "dur": 4003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755628621796742, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621797007, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621797292, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755628621797360, "dur": 1004518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621609253, "dur": 29563, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621638827, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_11065E1AD6288A19.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621638932, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621639137, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_D2083F65E1F978AD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621639227, "dur": 1117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_D2083F65E1F978AD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621640347, "dur": 949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7CCD9826AD528688.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621641334, "dur": 944, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7CCD9826AD528688.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621642447, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755628621643449, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Localization.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755628621643956, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621644379, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755628621645414, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11101650296562027598.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755628621645535, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621645651, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621646433, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621647078, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621648231, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-math-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1755628621647776, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621649661, "dur": 674, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.localization@7eec7ebf749d\\Editor\\UI\\Resources.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755628621648960, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621651003, "dur": 2329, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Utils\\EditModeUtils.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755628621650464, "dur": 2975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621653439, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621654557, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621655081, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621656274, "dur": 684, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@8199bdf7ecb0\\Editor\\GUI\\AssetInspectorGUI.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755628621655639, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621658067, "dur": 732, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Platforms\\AotStubWriterProvider.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755628621658799, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Platforms\\AotStubWriterAttribute.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755628621657006, "dur": 4689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621661695, "dur": 3446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621665141, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621665648, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621666727, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621667657, "dur": 2940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621670597, "dur": 3742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621674340, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621674687, "dur": 3299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621677987, "dur": 2965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621680952, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621682451, "dur": 793, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\WebApi\\WebRestApiClient.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755628621681840, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621683308, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621683520, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621683869, "dur": 922, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621684798, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621685520, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621685677, "dur": 801, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621686486, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621686760, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621687248, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621687909, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621688219, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621688484, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621688850, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621689608, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621689701, "dur": 1705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621691407, "dur": 1566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621692980, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621693306, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621693480, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621693668, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621693843, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621694322, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621694685, "dur": 2584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621697276, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755628621697456, "dur": 71627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621769094, "dur": 3230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/kcp2k.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621772325, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621772431, "dur": 4541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Localization.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621776973, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621777073, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621779829, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621779916, "dur": 3292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621783209, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621783291, "dur": 3505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621786797, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621787336, "dur": 4691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621792028, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621792279, "dur": 4519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755628621796799, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621796920, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755628621797024, "dur": 1004763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621609433, "dur": 29475, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621639129, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621639292, "dur": 2840, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_196DDF0992B5C827.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755628621642258, "dur": 451, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1755628621643453, "dur": 531, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1755628621643985, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621644386, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755628621644797, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621644982, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621645401, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9321109326917711913.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755628621645550, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621645628, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621646379, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621647039, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621647698, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621648375, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621648915, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621649247, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621649663, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\TimeFormat.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755628621649584, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621651230, "dur": 1089, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\VFXGraph\\VFXURPLitQuadStripOutput.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755628621650849, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621652488, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621653146, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621653785, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621654656, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621656249, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.localization@7eec7ebf749d\\Editor\\InternalBridge\\EditorGUIUtilityBridge.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755628621655426, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621658233, "dur": 732, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_0_1.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755628621656876, "dur": 2604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621659480, "dur": 3426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621662906, "dur": 2902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621665809, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621667154, "dur": 3285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621670440, "dur": 3743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621674184, "dur": 3427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621677612, "dur": 3423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621681036, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621682001, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621682657, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621683513, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755628621683764, "dur": 1552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621685316, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621685434, "dur": 2305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621687747, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755628621688084, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621688150, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621689041, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621689184, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755628621689540, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621689606, "dur": 1281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621690888, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621691062, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621691123, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Localization.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755628621691438, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Localization.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621692715, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621692825, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621692887, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621693085, "dur": 1408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621694497, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621694607, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755628621694848, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621695423, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621695554, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755628621695761, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621696260, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621696365, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755628621696474, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621696814, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621696927, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621697287, "dur": 71874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621769171, "dur": 3659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621772831, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621773226, "dur": 3049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621776282, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621776347, "dur": 4276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621780624, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621780802, "dur": 3501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621784304, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621784908, "dur": 3109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621788018, "dur": 614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621788637, "dur": 3028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621791666, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621791759, "dur": 4423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755628621796183, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621796635, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755628621797035, "dur": 1004730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621608908, "dur": 29838, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621638761, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621638852, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2D7A39E068E43F3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621639343, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621639505, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_34692D89E77A6B77.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621639621, "dur": 599, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621640228, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_200C40F278319369.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621641104, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621641242, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1755628621641775, "dur": 770, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755628621642553, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621642689, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1755628621643243, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621643504, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621643968, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621644226, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621644279, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755628621644371, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755628621644561, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621644628, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621644925, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621645529, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621646232, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621646863, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621648159, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\mscorlib.dll"}}, {"pid": 12345, "tid": 5, "ts": 1755628621647489, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621648875, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621649669, "dur": 668, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\InputForUI\\InputSystemProvider.cs"}}, {"pid": 12345, "tid": 5, "ts": 1755628621649495, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621650793, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621651543, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621652235, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621652904, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621653525, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621654205, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621654941, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621655724, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621657990, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugins\\Plugin.cs"}}, {"pid": 12345, "tid": 5, "ts": 1755628621656746, "dur": 1798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621658545, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Assignment\\IAssigner.cs"}}, {"pid": 12345, "tid": 5, "ts": 1755628621658545, "dur": 4979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621665133, "dur": 883, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.playmode@7f4b34d911b7\\Configurations\\Editor\\DefaultPlayModeConfig.cs"}}, {"pid": 12345, "tid": 5, "ts": 1755628621663525, "dur": 3236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621666762, "dur": 2104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621668866, "dur": 3404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621672270, "dur": 3739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621676010, "dur": 3880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621679890, "dur": 1897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621681787, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621682503, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621683249, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621683914, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621684196, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621684275, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621685062, "dur": 1325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621686423, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621686863, "dur": 1086, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621687953, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621688430, "dur": 929, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621689392, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621689567, "dur": 1192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621690760, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621690935, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621691372, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621691703, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621692101, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621692263, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621692696, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621692794, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755628621692965, "dur": 1528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621694494, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621694725, "dur": 2564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621697290, "dur": 71890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621769190, "dur": 4165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Transports.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621773363, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621773569, "dur": 3795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621777365, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621777484, "dur": 4563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621782048, "dur": 896, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621782955, "dur": 3883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621786839, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621787009, "dur": 3764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621790774, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621790932, "dur": 3815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.022.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755628621794748, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621794909, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621795207, "dur": 857, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621796073, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621796389, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621796457, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755628621796850, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1755628621797053, "dur": 1004855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621609515, "dur": 29639, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621639158, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_8AA5619BE74CBCE9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755628621639297, "dur": 3973, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_8AA5619BE74CBCE9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755628621643339, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621643664, "dur": 682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621644382, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1755628621644605, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621644913, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621645406, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755628621645539, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621646383, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621647054, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621647689, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621648355, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621649638, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.localization@7eec7ebf749d\\Editor\\Icons\\EditorIcons.cs"}}, {"pid": 12345, "tid": 6, "ts": 1755628621649401, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621650629, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621651213, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621651904, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621652576, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621653245, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621653890, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621654587, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621655239, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621655947, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621658315, "dur": 634, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\Acknowledgements\\Acknowledgement_DotNetZip.cs"}}, {"pid": 12345, "tid": 6, "ts": 1755628621656902, "dur": 2048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621658950, "dur": 4492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621663443, "dur": 2823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621666267, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621668086, "dur": 2697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621670783, "dur": 3815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621674599, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621675597, "dur": 3487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621679085, "dur": 2235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621681326, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621682305, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621683015, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621683254, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621683511, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755628621684127, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621685250, "dur": 1174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621686468, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755628621686766, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621687109, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621687707, "dur": 1367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621689088, "dur": 734, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621689827, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755628621689960, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621690241, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621690819, "dur": 1367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621692203, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1755628621693013, "dur": 219, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621693776, "dur": 70168, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1755628621769052, "dur": 2722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621771775, "dur": 721, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621772503, "dur": 3276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleWebTransport.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621775781, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621775965, "dur": 3250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621779216, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621779340, "dur": 3098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Authenticators.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621782440, "dur": 1292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621783744, "dur": 2807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621786552, "dur": 2027, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621788592, "dur": 3628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621792221, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621792343, "dur": 3606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Edgegap.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755628621795950, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621796440, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621796569, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628621797013, "dur": 525254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755628622322329, "dur": 479103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1755628622322269, "dur": 479166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1755628622801437, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621609384, "dur": 29516, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621639139, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B38A951B110D020A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621639316, "dur": 1770, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B38A951B110D020A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621641244, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755628621641498, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621641653, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755628621641710, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621641769, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755628621641935, "dur": 741, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1755628621642680, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621643018, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621643942, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621644153, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621644346, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1755628621644925, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621645588, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621646542, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621647161, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621648098, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621648750, "dur": 1572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.playmode@7f4b34d911b7\\Scenarios\\Editor\\PlayModeConfig\\LocalInstanceDescription.cs"}}, {"pid": 12345, "tid": 7, "ts": 1755628621648492, "dur": 2127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621650619, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621651307, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621651988, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621652645, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621653325, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621654018, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621654678, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621655463, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621656420, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621658184, "dur": 719, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\Colors\\SkinnedColor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1755628621657291, "dur": 4341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621661632, "dur": 3477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621665110, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621666405, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621667535, "dur": 2814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621670350, "dur": 3541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621673891, "dur": 2949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621676841, "dur": 3597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621680439, "dur": 1739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621682448, "dur": 817, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Changesets\\ChangesetsSelection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1755628621682179, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621683443, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621683517, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621683863, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621683927, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621684538, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621685237, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621685302, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621685614, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621686417, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621686595, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621686818, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621687465, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621687933, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Localization.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621688301, "dur": 1253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Localization.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621689554, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621689697, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621690865, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621691074, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621691360, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621691421, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621691750, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621691807, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621692383, "dur": 1002, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621693389, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621693594, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621693800, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621694448, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621694973, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621695083, "dur": 2184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621697275, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755628621697539, "dur": 71534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621769082, "dur": 3293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621772384, "dur": 655, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621773045, "dur": 3731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Telepathy.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621776777, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621776914, "dur": 4137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621781052, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621781369, "dur": 2979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/EncryptionTransportEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621784349, "dur": 1809, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621786163, "dur": 3380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621789544, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621789641, "dur": 4238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621793880, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621794023, "dur": 3186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.013.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755628621797210, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755628621797290, "dur": 1004656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621609550, "dur": 29684, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621639272, "dur": 1078, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_18A3D624D5A4F4D6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755628621640351, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A9644AD1997CC33F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755628621640628, "dur": 822, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A9644AD1997CC33F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755628621641454, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621641576, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755628621641675, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755628621641873, "dur": 1157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1755628621643449, "dur": 550, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1755628621644023, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755628621644507, "dur": 8396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621652905, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621653268, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Graphs\\VirtualTextureInputMaterialSlot.cs"}}, {"pid": 12345, "tid": 8, "ts": 1755628621653100, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621654411, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621655123, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621655919, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621658405, "dur": 669, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyOptionTreeExtensionProvider.cs"}}, {"pid": 12345, "tid": 8, "ts": 1755628621657174, "dur": 4515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621661690, "dur": 2997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621664688, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621665909, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621667590, "dur": 2705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621670296, "dur": 3251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621673548, "dur": 3523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621677071, "dur": 3758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621680829, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621681960, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621682523, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621683287, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621683571, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755628621684199, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621684357, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621685152, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621685335, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621685441, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755628621685659, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621685734, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621686752, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621686915, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621687610, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621687849, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755628621688121, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621688210, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621688952, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621689204, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621690083, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621690222, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621690341, "dur": 1387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621691728, "dur": 2187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621693955, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621694772, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621694919, "dur": 2435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621697355, "dur": 71817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621769181, "dur": 1786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621770969, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621771182, "dur": 4431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621775614, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621776287, "dur": 3872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621780160, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621780260, "dur": 3468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621783729, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621784282, "dur": 4060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621788343, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621788767, "dur": 3514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621792282, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621792516, "dur": 4194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755628621796711, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621796798, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621796975, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755628621797032, "dur": 1004743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621609486, "dur": 29663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621639224, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_C4E65B30254244B8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621639968, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621640192, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_95189FE831A76367.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621640295, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621640584, "dur": 3413, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_2A749ACEEBE3A48E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621644020, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621644577, "dur": 8985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621653563, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621653661, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621653813, "dur": 27200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621681083, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621681326, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621681489, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621681554, "dur": 1785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621683340, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621683502, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621683709, "dur": 1500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621685213, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621685938, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621686094, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621686355, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621686514, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621687294, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621687450, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621687789, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621688005, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621688143, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621688661, "dur": 1085, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621689750, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621690573, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621690980, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621691210, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621691775, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621692040, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621692335, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621693008, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621693342, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621693546, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621693763, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621694549, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621694948, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621695502, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621695688, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621696199, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621696304, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755628621696512, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621696856, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621697278, "dur": 73776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621771055, "dur": 3100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621774156, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621774270, "dur": 3372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621777643, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621777719, "dur": 1759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621779479, "dur": 2663, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621782150, "dur": 2168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621784325, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621784385, "dur": 2169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621786555, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621786867, "dur": 3868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621790736, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621790811, "dur": 3002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621793813, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621793898, "dur": 2859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755628621796757, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755628621796957, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Notifications.Android.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1755628621797017, "dur": 1004795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621609582, "dur": 29798, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621639631, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621640305, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621640584, "dur": 845, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A5F21C0EBB6C174C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755628621641432, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755628621641862, "dur": 2135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1755628621643998, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621644439, "dur": 1025, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1755628621645559, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621646008, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621646478, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621646953, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621647405, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621648013, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621648733, "dur": 1574, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mobile.notifications@439e41e635a0\\Editor\\NotificationSettings.cs"}}, {"pid": 12345, "tid": 10, "ts": 1755628621648398, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621650308, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621651015, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621651743, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621652449, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621653163, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621653813, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621654502, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621655180, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621655862, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621656767, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621658010, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Other\\DictionaryAssetEditor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1755628621657873, "dur": 5045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621662919, "dur": 2981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621665901, "dur": 2398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621668300, "dur": 3181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621671482, "dur": 3768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621675252, "dur": 3258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621678512, "dur": 2399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621681423, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\AssetEditor\\InputActionAssetManager.cs"}}, {"pid": 12345, "tid": 10, "ts": 1755628621680911, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621682395, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621682876, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621683153, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621683238, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621683510, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755628621683905, "dur": 1283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621685189, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621685606, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621685667, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755628621685862, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621685936, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621686798, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621687005, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621687082, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755628621687313, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621687376, "dur": 1882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621689258, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621689357, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755628621689742, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621690401, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621690588, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621691675, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621691902, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621692731, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621692822, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621693414, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621693580, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755628621693836, "dur": 868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621694705, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621694814, "dur": 2463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621697277, "dur": 71784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621769064, "dur": 3544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621772609, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621772984, "dur": 5552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621778537, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621778620, "dur": 2938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621781558, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621781618, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621783991, "dur": 1970, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621785967, "dur": 2287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621788255, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621788707, "dur": 2874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621791582, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621791912, "dur": 2815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755628621794727, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621794797, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621795334, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621795444, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621795839, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621795938, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621796418, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621796546, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621796698, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621796765, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755628621796933, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Mirror.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1755628621797030, "dur": 1004753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621609609, "dur": 30047, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621639710, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621639840, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_22EE298EC1BAB2B0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621640006, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621640297, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621640568, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_DDA8E091E3967B34.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621640817, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_674D8A39E28088FE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621641257, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755628621641595, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621641944, "dur": 600, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755628621642565, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621642916, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755628621642992, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621643963, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621644377, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755628621645566, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621646154, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621646612, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621647069, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621648109, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 11, "ts": 1755628621647762, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621648879, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621649189, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621649666, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineWindow_TimeCursor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1755628621651005, "dur": 1454, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineWindow_HeaderGui.cs"}}, {"pid": 12345, "tid": 11, "ts": 1755628621649552, "dur": 2983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621652535, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621653032, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621653965, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621654445, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621654928, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621655502, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621656019, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621658344, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Special\\TypeHandleInspector.cs"}}, {"pid": 12345, "tid": 11, "ts": 1755628621657343, "dur": 4881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621662225, "dur": 3202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621665427, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621665906, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621667247, "dur": 3010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621670257, "dur": 3612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621673870, "dur": 3804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621677674, "dur": 3503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621681234, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621682228, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621682727, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621683283, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621683502, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621683705, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621683964, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.013.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621684173, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.013.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621684849, "dur": 894, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621685751, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621685869, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621686331, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621686520, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621687049, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621687200, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621687830, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621687993, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621688188, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621688772, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621689284, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621689372, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621689522, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621689916, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621690075, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621690154, "dur": 1639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621691794, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621692007, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621692253, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621692424, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621693175, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621693327, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621693540, "dur": 855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621694395, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621694699, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755628621694878, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621695299, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621695392, "dur": 1889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621697282, "dur": 71765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621769050, "dur": 4280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621773330, "dur": 2431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621775771, "dur": 2374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621778146, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621778217, "dur": 3537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621781755, "dur": 2189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621783951, "dur": 3008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621786959, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621790083, "dur": 273, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 11, "ts": 1755628621790357, "dur": 1335, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 11, "ts": 1755628621791693, "dur": 76, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 11, "ts": 1755628621787085, "dur": 4684, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621791770, "dur": 3258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755628621795028, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621795483, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621795773, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621795960, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755628621797045, "dur": 1004726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621609723, "dur": 30956, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621640713, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5F1525A9150C0C6C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755628621640816, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6926138A25866A37.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755628621640956, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.APIComparison.Framework.dll_795DFBA713DFDA95.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755628621641095, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621641246, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621641320, "dur": 743, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755628621642114, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621642275, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621642619, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621642753, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621643245, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621643627, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755628621643715, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621644357, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755628621644496, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621644559, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621644884, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621645396, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755628621645593, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621646246, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621646882, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621647562, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621648129, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621648632, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621649659, "dur": 680, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.localization@7eec7ebf749d\\Editor\\Plugins\\CSV\\CSV.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755628621649269, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621650609, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621651452, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621651901, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621652355, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621652805, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621653278, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621653746, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621654206, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621654709, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621655337, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621655829, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621658022, "dur": 611, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_5.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755628621658634, "dur": 595, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_4.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755628621656866, "dur": 3049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621659915, "dur": 3866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621663781, "dur": 1975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621665757, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621667365, "dur": 3073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621670438, "dur": 3523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621673961, "dur": 3329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621677291, "dur": 3634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621680926, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621681918, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621682423, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621683267, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621683500, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755628621683766, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621684469, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621684741, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621685576, "dur": 1369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621686958, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621687641, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621687978, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755628621688209, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621688279, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755628621688469, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755628621688692, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621689275, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621690050, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755628621690369, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621691680, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621691830, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621692733, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621692836, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621693512, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621693666, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755628621693933, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621694567, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621694810, "dur": 2487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621697297, "dur": 71893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621769198, "dur": 5016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621774221, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621774737, "dur": 3300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621778038, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621778609, "dur": 3513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.013.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621782123, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621782183, "dur": 2808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621784992, "dur": 924, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621785927, "dur": 2636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621788564, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621788671, "dur": 2706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621791378, "dur": 958, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621792346, "dur": 2699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755628621795046, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621795415, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621796203, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755628621796845, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1755628621797025, "dur": 1004916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621609672, "dur": 30656, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621640332, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CBC203599E566618.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621640941, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621641028, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621641113, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_208F1D0164BFDE20.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621641186, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621641272, "dur": 2136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1755628621643545, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1755628621643964, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621644387, "dur": 556, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Localization.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1755628621645407, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1755628621645733, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621646181, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621646622, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621647091, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621647623, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621648309, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621648959, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621649649, "dur": 639, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\ObjectReferenceField.cs"}}, {"pid": 12345, "tid": 13, "ts": 1755628621649617, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621650933, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621651628, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621652275, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621652982, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621653816, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621654365, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621654839, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621655402, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621655911, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621658050, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\TextureResolution.cs"}}, {"pid": 12345, "tid": 13, "ts": 1755628621657016, "dur": 4461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621661478, "dur": 3657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621665136, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621666047, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621667161, "dur": 2871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621670033, "dur": 3603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621673637, "dur": 3267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621676904, "dur": 3757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621680662, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621681776, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621682701, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621683370, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621683522, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621683766, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621683826, "dur": 1219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621685046, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621685263, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621685595, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621685702, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621685969, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621686869, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621687517, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621687586, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621687687, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621687744, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621688736, "dur": 997, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621689734, "dur": 1618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621691352, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621691953, "dur": 1799, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621693757, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621693945, "dur": 1130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621695079, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621695639, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621695720, "dur": 1545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621697267, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755628621697474, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621697784, "dur": 71448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621769243, "dur": 4274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Examples.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621773518, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621774043, "dur": 3349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621777392, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621777723, "dur": 2955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621780679, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621780771, "dur": 2042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621782820, "dur": 977, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621783802, "dur": 2658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621786461, "dur": 2198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621788669, "dur": 2627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621791296, "dur": 971, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621792274, "dur": 3238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755628621795562, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621795840, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621796434, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621796755, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755628621796842, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mobile.AndroidLogcat.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1755628621797019, "dur": 1004791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621609698, "dur": 30645, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621640349, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_FEB99ECBF3C51413.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755628621640703, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621641060, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621641293, "dur": 733, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1755628621642053, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755628621642324, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621642413, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621642472, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1755628621643432, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1755628621643713, "dur": 708, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621644421, "dur": 1310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755628621645732, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621646279, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621646730, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621647224, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621648167, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621648853, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621649670, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineWindowTimeControl.cs"}}, {"pid": 12345, "tid": 14, "ts": 1755628621649571, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621650881, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621651663, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621652328, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621652786, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621653257, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621653818, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621654540, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621655201, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621655895, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621656715, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621658037, "dur": 712, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Dependencies\\ReorderableList\\IReorderableListDropTarget.cs"}}, {"pid": 12345, "tid": 14, "ts": 1755628621658037, "dur": 5496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621663534, "dur": 2351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621665886, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621667303, "dur": 3086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621670389, "dur": 3012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621673401, "dur": 3451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621676853, "dur": 3514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621681574, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\UITKAssetEditor\\Views\\BindingPropertiesView.cs"}}, {"pid": 12345, "tid": 14, "ts": 1755628621680368, "dur": 1995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621682364, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621683021, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621683144, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621683234, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621683512, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755628621683932, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621684574, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621684800, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621684883, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755628621685152, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621685293, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621685953, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621686122, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755628621686487, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621687302, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621687494, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621687736, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621687873, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755628621688132, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755628621688384, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755628621688573, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621689024, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621689547, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621689618, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621690132, "dur": 3496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621693641, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621694139, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621694483, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621694613, "dur": 897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621695511, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755628621695702, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621696247, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621696393, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621697283, "dur": 73752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621771043, "dur": 2939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621773983, "dur": 1847, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621775838, "dur": 3085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621778932, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621779301, "dur": 2578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621781880, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621782109, "dur": 2646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621784809, "dur": 5153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621789963, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621790380, "dur": 2250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621792631, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755628621792749, "dur": 3834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755628621797020, "dur": 1004778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621609774, "dur": 31498, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621641319, "dur": 788, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755628621642444, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755628621643461, "dur": 526, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1755628621643988, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621644407, "dur": 1027, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1755628621645692, "dur": 1431, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@dcc61ebd6655\\Editor\\PropertyDrawers\\VcamTargetPropertyDrawer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1755628621645558, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621647492, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621648218, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621648732, "dur": 2293, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.playmode@7f4b34d911b7\\Scenarios\\Editor\\Api\\StageGraph.cs"}}, {"pid": 12345, "tid": 15, "ts": 1755628621648658, "dur": 2887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621651546, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621652417, "dur": 1462, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Drawing\\Views\\GradientEdge.cs"}}, {"pid": 12345, "tid": 15, "ts": 1755628621652192, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621654189, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621654685, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621655299, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621655794, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621656657, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621658077, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Context\\GraphContextProvider.cs"}}, {"pid": 12345, "tid": 15, "ts": 1755628621658077, "dur": 3769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621661847, "dur": 3790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621665638, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621666220, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621667774, "dur": 2607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621670381, "dur": 3846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621674227, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621675463, "dur": 2601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621678064, "dur": 3295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621681360, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621682190, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621682694, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621683366, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621683526, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755628621684102, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621684161, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621684976, "dur": 1023, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621686010, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621686064, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755628621686258, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621686897, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621687362, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.013.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621688160, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621688288, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621688548, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755628621688833, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621689619, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621690045, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755628621690340, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621690559, "dur": 2158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621692717, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621692872, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621693521, "dur": 632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621694164, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621694366, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621694439, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621694513, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621694611, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755628621695069, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621695481, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621695805, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621695915, "dur": 1412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621697328, "dur": 71725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621769074, "dur": 2182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621771257, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621771364, "dur": 1877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621773242, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621773366, "dur": 3505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621776872, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621777143, "dur": 3255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621780453, "dur": 5412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621785871, "dur": 1523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621787401, "dur": 2183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621789585, "dur": 3117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621792744, "dur": 3306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Localization.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755628621796051, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621796500, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621796601, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628621797016, "dur": 541508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755628622338591, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1755628622338526, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1755628622338719, "dur": 3001, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1755628622341726, "dur": 460133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621609747, "dur": 31102, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621640857, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621641055, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621641290, "dur": 616, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755628621641941, "dur": 1063, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1755628621643606, "dur": 380, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755628621643988, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621644417, "dur": 1308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1755628621645725, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621646487, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621646845, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621647378, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621648003, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621648739, "dur": 1560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\OnBoarding\\SectionsFinder.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755628621650299, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\OnBoarding\\QuickstartPackageHandling.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755628621648705, "dur": 2710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621651415, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621651877, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621652357, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621652817, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621653284, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621653750, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621654261, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621654768, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621655330, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621655831, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621656616, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621658015, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Description\\MacroDescriptor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755628621658677, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Description\\MachineDescription.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755628621658015, "dur": 5390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621665437, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.playmode@7f4b34d911b7\\Workflow\\Editor\\EditorSystems\\MainPlayerSystems.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755628621663406, "dur": 2944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621666351, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621668199, "dur": 3395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621671595, "dur": 3649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621675245, "dur": 3337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621678583, "dur": 2661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621681245, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621681967, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621682428, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621683250, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621683505, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755628621683839, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621683923, "dur": 1981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621685904, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621686056, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755628621686209, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621686278, "dur": 1721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621687999, "dur": 1114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621689124, "dur": 870, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621689999, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755628621690248, "dur": 2200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621692449, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621692917, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621693423, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755628621693643, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621694448, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621694604, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755628621694947, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621695800, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621695950, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755628621696139, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621696602, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755628621696708, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621697269, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755628621697365, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621697471, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621697814, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621698865, "dur": 172107, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621874983, "dur": 41751, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 16, "ts": 1755628621874644, "dur": 42196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755628621916842, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628621916953, "dur": 247406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1755628621916950, "dur": 250875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755628622171848, "dur": 476, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755628622173072, "dur": 131502, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755628622322235, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1755628622322219, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1755628622322354, "dur": 479466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755628622817534, "dur": 5091, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 13804, "tid": 164895, "ts": 1755628622846269, "dur": 5189, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 13804, "tid": 164895, "ts": 1755628622851526, "dur": 2322, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 13804, "tid": 164895, "ts": 1755628622837912, "dur": 17076, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}