<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: GetEntry Pages: 1 -->
<svg width="695pt" height="673pt"
 viewBox="0.00 0.00 695.39 673.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="page0,1_graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 669)">
<title>GetEntry</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-669 691.39,-669 691.39,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_entry</title>
<path fill="#daedfd" stroke="#2196f3" d="M30.27,-63C30.27,-63 655.27,-63 655.27,-63 661.27,-63 667.27,-69 667.27,-75 667.27,-75 667.27,-590 667.27,-590 667.27,-596 661.27,-602 655.27,-602 655.27,-602 30.27,-602 30.27,-602 24.27,-602 18.27,-596 18.27,-590 18.27,-590 18.27,-75 18.27,-75 18.27,-69 24.27,-63 30.27,-63"/>
<text text-anchor="middle" x="57.27" y="-590" font-family="inter" font-size="10.00">Extract Entry</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_override</title>
<path fill="#fff7e1" stroke="#ffcb27" d="M38.27,-71C38.27,-71 429.27,-71 429.27,-71 435.27,-71 441.27,-77 441.27,-83 441.27,-83 441.27,-242 441.27,-242 441.27,-248 435.27,-254 429.27,-254 429.27,-254 38.27,-254 38.27,-254 32.27,-254 26.27,-248 26.27,-242 26.27,-242 26.27,-83 26.27,-83 26.27,-77 32.27,-71 38.27,-71"/>
<text text-anchor="middle" x="68.77" y="-242" font-family="inter" font-size="10.00">Entry Override</text>
</g>
<!-- getEntryFromTable -->
<g id="node1" class="node">
<title>getEntryFromTable</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M249.27,-665C249.27,-665 159.27,-665 159.27,-665 153.27,-665 147.27,-659 147.27,-653 147.27,-653 147.27,-641 147.27,-641 147.27,-635 153.27,-629 159.27,-629 159.27,-629 249.27,-629 249.27,-629 255.27,-629 261.27,-635 261.27,-641 261.27,-641 261.27,-653 261.27,-653 261.27,-659 255.27,-665 249.27,-665"/>
<text text-anchor="middle" x="204.27" y="-644.5" font-family="inter" font-size="10.00">Get Entry from table</text>
</g>
<!-- getEntry -->
<g id="node4" class="node">
<title>getEntry</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M250.27,-575C250.27,-575 158.27,-575 158.27,-575 152.27,-575 146.27,-569 146.27,-563 146.27,-563 146.27,-551 146.27,-551 146.27,-545 152.27,-539 158.27,-539 158.27,-539 250.27,-539 250.27,-539 256.27,-539 262.27,-545 262.27,-551 262.27,-551 262.27,-563 262.27,-563 262.27,-569 256.27,-575 250.27,-575"/>
<text text-anchor="middle" x="204.27" y="-560" font-family="inter" font-size="10.00">Get Entry using</text>
<text text-anchor="middle" x="204.27" y="-549" font-family="inter" font-size="10.00">TableEntryReference</text>
</g>
<!-- getEntryFromTable&#45;&gt;getEntry -->
<g id="edge9" class="edge">
<title>getEntryFromTable&#45;&gt;getEntry</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M204.27,-628.61C204.27,-616.24 204.27,-599.37 204.27,-585.22"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="207.77,-585.05 204.27,-575.05 200.77,-585.05 207.77,-585.05"/>
</g>
<!-- isValidEntry -->
<g id="node2" class="node">
<title>isValidEntry</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M352.88,-496C352.88,-496 302.66,-467 302.66,-467 297.47,-464 297.47,-458 302.66,-455 302.66,-455 352.88,-426 352.88,-426 358.07,-423 368.47,-423 373.66,-426 373.66,-426 423.88,-455 423.88,-455 429.07,-458 429.07,-464 423.88,-467 423.88,-467 373.66,-496 373.66,-496 368.47,-499 358.07,-499 352.88,-496"/>
<text text-anchor="middle" x="363.27" y="-469.5" font-family="inter" font-size="10.00">Was a valid</text>
<text text-anchor="middle" x="363.27" y="-458.5" font-family="inter" font-size="10.00">entry</text>
<text text-anchor="middle" x="363.27" y="-447.5" font-family="inter" font-size="10.00">returned?</text>
</g>
<!-- containOverride -->
<g id="node5" class="node">
<title>containOverride</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M352.35,-368.03C352.35,-368.03 284.19,-336.97 284.19,-336.97 278.73,-334.49 278.73,-329.51 284.19,-327.03 284.19,-327.03 352.35,-295.97 352.35,-295.97 357.81,-293.49 368.73,-293.49 374.19,-295.97 374.19,-295.97 442.35,-327.03 442.35,-327.03 447.81,-329.51 447.81,-334.49 442.35,-336.97 442.35,-336.97 374.19,-368.03 374.19,-368.03 368.73,-370.51 357.81,-370.51 352.35,-368.03"/>
<text text-anchor="middle" x="363.27" y="-340.5" font-family="inter" font-size="10.00">Does the Entry</text>
<text text-anchor="middle" x="363.27" y="-329.5" font-family="inter" font-size="10.00">contain an</text>
<text text-anchor="middle" x="363.27" y="-318.5" font-family="inter" font-size="10.00">IEntryOverride?</text>
</g>
<!-- isValidEntry&#45;&gt;containOverride -->
<g id="edge2" class="edge">
<title>isValidEntry&#45;&gt;containOverride</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M363.27,-419.82C363.27,-408.35 363.27,-395.71 363.27,-383.7"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="366.77,-383.36 363.27,-373.36 359.77,-383.36 366.77,-383.36"/>
<text text-anchor="middle" x="372.27" y="-394" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- hasFallback -->
<g id="node9" class="node">
<title>hasFallback</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M554.27,-368.2C554.27,-368.2 482.27,-336.8 482.27,-336.8 476.77,-334.4 476.77,-329.6 482.27,-327.2 482.27,-327.2 554.27,-295.8 554.27,-295.8 559.77,-293.4 570.77,-293.4 576.27,-295.8 576.27,-295.8 648.27,-327.2 648.27,-327.2 653.77,-329.6 653.77,-334.4 648.27,-336.8 648.27,-336.8 576.27,-368.2 576.27,-368.2 570.77,-370.6 559.77,-370.6 554.27,-368.2"/>
<text text-anchor="middle" x="565.27" y="-340.5" font-family="inter" font-size="10.00">Does the Locale</text>
<text text-anchor="middle" x="565.27" y="-329.5" font-family="inter" font-size="10.00">contain a</text>
<text text-anchor="middle" x="565.27" y="-318.5" font-family="inter" font-size="10.00">Fallback?</text>
</g>
<!-- isValidEntry&#45;&gt;hasFallback -->
<g id="edge4" class="edge">
<title>isValidEntry&#45;&gt;hasFallback</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M396.63,-439.03C429.93,-418.09 481.39,-385.74 518.7,-362.28"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="520.79,-365.1 527.39,-356.82 517.06,-359.18 520.79,-365.1"/>
<text text-anchor="middle" x="477.27" y="-394" font-family="inter" font-size="10.00">No</text>
</g>
<!-- overrideEntry -->
<g id="node3" class="node">
<title>overrideEntry</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M158.27,-117.5C158.27,-117.5 46.27,-117.5 46.27,-117.5 40.27,-117.5 34.27,-111.5 34.27,-105.5 34.27,-105.5 34.27,-93.5 34.27,-93.5 34.27,-87.5 40.27,-81.5 46.27,-81.5 46.27,-81.5 158.27,-81.5 158.27,-81.5 164.27,-81.5 170.27,-87.5 170.27,-93.5 170.27,-93.5 170.27,-105.5 170.27,-105.5 170.27,-111.5 164.27,-117.5 158.27,-117.5"/>
<text text-anchor="middle" x="102.27" y="-97" font-family="inter" font-size="10.00">Use the overridden entry</text>
</g>
<!-- overrideEntry&#45;&gt;getEntry -->
<g id="edge8" class="edge">
<title>overrideEntry&#45;&gt;getEntry</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M112.77,-117.76C123.08,-136.37 137.27,-167.22 137.27,-196 137.27,-462 137.27,-462 137.27,-462 137.27,-489.53 156.56,-514.58 174.44,-531.89"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="172.24,-534.61 181.96,-538.82 176.98,-529.46 172.24,-534.61"/>
</g>
<!-- getEntry&#45;&gt;isValidEntry -->
<g id="edge1" class="edge">
<title>getEntry&#45;&gt;isValidEntry</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M233.06,-538.98C257.37,-524.61 292.53,-503.82 320.19,-487.47"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="322.05,-490.43 328.88,-482.33 318.49,-484.41 322.05,-490.43"/>
</g>
<!-- overrideType -->
<g id="node6" class="node">
<title>overrideType</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M352.65,-221.41C352.65,-221.41 316.89,-202.59 316.89,-202.59 311.58,-199.79 311.58,-194.21 316.89,-191.41 316.89,-191.41 352.65,-172.59 352.65,-172.59 357.96,-169.79 368.58,-169.79 373.89,-172.59 373.89,-172.59 409.65,-191.41 409.65,-191.41 414.96,-194.21 414.96,-199.79 409.65,-202.59 409.65,-202.59 373.89,-221.41 373.89,-221.41 368.58,-224.21 357.96,-224.21 352.65,-221.41"/>
<text text-anchor="middle" x="363.27" y="-200" font-family="inter" font-size="10.00">Override</text>
<text text-anchor="middle" x="363.27" y="-189" font-family="inter" font-size="10.00">Type</text>
</g>
<!-- containOverride&#45;&gt;overrideType -->
<g id="edge3" class="edge">
<title>containOverride&#45;&gt;overrideType</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M363.27,-290.73C363.27,-273.77 363.27,-254.08 363.27,-237.23"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="366.77,-237.15 363.27,-227.15 359.77,-237.15 366.77,-237.15"/>
<text text-anchor="middle" x="372.27" y="-265" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- returnEntry -->
<g id="node12" class="node">
<title>returnEntry</title>
<path fill="#e9f4e9" stroke="#67bc6b" d="M493.27,-36C493.27,-36 441.27,-36 441.27,-36 435.27,-36 429.27,-30 429.27,-24 429.27,-24 429.27,-12 429.27,-12 429.27,-6 435.27,0 441.27,0 441.27,0 493.27,0 493.27,0 499.27,0 505.27,-6 505.27,-12 505.27,-12 505.27,-24 505.27,-24 505.27,-30 499.27,-36 493.27,-36"/>
<text text-anchor="middle" x="467.27" y="-15.5" font-family="inter" font-size="10.00">Return entry</text>
</g>
<!-- containOverride&#45;&gt;returnEntry -->
<g id="edge13" class="edge">
<title>containOverride&#45;&gt;returnEntry</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M398.97,-307.05C416,-293.59 434.88,-275.22 445.27,-254 478.95,-185.17 475.17,-91.5 470.68,-46.01"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="474.15,-45.6 469.6,-36.03 467.19,-46.35 474.15,-45.6"/>
<text text-anchor="middle" x="479.27" y="-141" font-family="inter" font-size="10.00">No</text>
</g>
<!-- overrideType&#45;&gt;overrideEntry -->
<g id="edge7" class="edge">
<title>overrideType&#45;&gt;overrideEntry</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M335.04,-181.69C324.84,-176.76 313.15,-171.38 302.27,-167 258.77,-149.5 208.71,-132.86 169.55,-120.6"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="170.43,-117.21 159.84,-117.58 168.35,-123.89 170.43,-117.21"/>
<text text-anchor="middle" x="265.77" y="-141" font-family="inter" font-size="10.00">Entry</text>
</g>
<!-- overrideTable -->
<g id="node7" class="node">
<title>overrideTable</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M289.77,-120C289.77,-120 200.77,-120 200.77,-120 194.77,-120 188.77,-114 188.77,-108 188.77,-108 188.77,-91 188.77,-91 188.77,-85 194.77,-79 200.77,-79 200.77,-79 289.77,-79 289.77,-79 295.77,-79 301.77,-85 301.77,-91 301.77,-91 301.77,-108 301.77,-108 301.77,-114 295.77,-120 289.77,-120"/>
<text text-anchor="middle" x="245.27" y="-108" font-family="inter" font-size="10.00">Use the same</text>
<text text-anchor="middle" x="245.27" y="-97" font-family="inter" font-size="10.00">entry but from</text>
<text text-anchor="middle" x="245.27" y="-86" font-family="inter" font-size="10.00">the overridden table</text>
</g>
<!-- overrideType&#45;&gt;overrideTable -->
<g id="edge5" class="edge">
<title>overrideType&#45;&gt;overrideTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M341.35,-178.26C323.43,-163.75 297.82,-143.03 277.55,-126.63"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="279.49,-123.69 269.51,-120.12 275.08,-129.13 279.49,-123.69"/>
<text text-anchor="middle" x="317.77" y="-141" font-family="inter" font-size="10.00">Table</text>
</g>
<!-- overrideTableAndEntry -->
<g id="node8" class="node">
<title>overrideTableAndEntry</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M420.77,-120C420.77,-120 331.77,-120 331.77,-120 325.77,-120 319.77,-114 319.77,-108 319.77,-108 319.77,-91 319.77,-91 319.77,-85 325.77,-79 331.77,-79 331.77,-79 420.77,-79 420.77,-79 426.77,-79 432.77,-85 432.77,-91 432.77,-91 432.77,-108 432.77,-108 432.77,-114 426.77,-120 420.77,-120"/>
<text text-anchor="middle" x="376.27" y="-108" font-family="inter" font-size="10.00">Use the overriden</text>
<text text-anchor="middle" x="376.27" y="-97" font-family="inter" font-size="10.00">entry from</text>
<text text-anchor="middle" x="376.27" y="-86" font-family="inter" font-size="10.00">the overridden table</text>
</g>
<!-- overrideType&#45;&gt;overrideTableAndEntry -->
<g id="edge6" class="edge">
<title>overrideType&#45;&gt;overrideTableAndEntry</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M361.39,-167.82C361.26,-158.29 361.65,-147.63 363.27,-138 363.73,-135.29 364.36,-132.52 365.09,-129.76"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="368.49,-130.62 368.12,-120.03 361.81,-128.54 368.49,-130.62"/>
<text text-anchor="middle" x="398.27" y="-141" font-family="inter" font-size="10.00">TableAndEntry</text>
</g>
<!-- loadTable -->
<g id="node11" class="node">
<title>loadTable</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M296.77,-36C296.77,-36 193.77,-36 193.77,-36 187.77,-36 181.77,-30 181.77,-24 181.77,-24 181.77,-12 181.77,-12 181.77,-6 187.77,0 193.77,0 193.77,0 296.77,0 296.77,0 302.77,0 308.77,-6 308.77,-12 308.77,-12 308.77,-24 308.77,-24 308.77,-30 302.77,-36 296.77,-36"/>
<text text-anchor="middle" x="245.27" y="-15.5" font-family="inter" font-size="10.00">Request override Table</text>
</g>
<!-- overrideTable&#45;&gt;loadTable -->
<g id="edge10" class="edge">
<title>overrideTable&#45;&gt;loadTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" stroke-dasharray="5,2" d="M245.27,-78.85C245.27,-68.99 245.27,-56.88 245.27,-46.15"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="248.77,-46 245.27,-36 241.77,-46 248.77,-46"/>
</g>
<!-- overrideTableAndEntry&#45;&gt;loadTable -->
<g id="edge11" class="edge">
<title>overrideTableAndEntry&#45;&gt;loadTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" stroke-dasharray="5,2" d="M343.89,-78.85C325.1,-67.44 301.37,-53.05 281.9,-41.23"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="283.65,-38.2 273.29,-36 280.02,-44.18 283.65,-38.2"/>
</g>
<!-- useFallback -->
<g id="node10" class="node">
<title>useFallback</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M624.77,-36C624.77,-36 535.77,-36 535.77,-36 529.77,-36 523.77,-30 523.77,-24 523.77,-24 523.77,-12 523.77,-12 523.77,-6 529.77,0 535.77,0 535.77,0 624.77,0 624.77,0 630.77,0 636.77,-6 636.77,-12 636.77,-12 636.77,-24 636.77,-24 636.77,-30 630.77,-36 624.77,-36"/>
<text text-anchor="middle" x="580.27" y="-21" font-family="inter" font-size="10.00">Request Table using</text>
<text text-anchor="middle" x="580.27" y="-10" font-family="inter" font-size="10.00">fallback Locale</text>
</g>
<!-- hasFallback&#45;&gt;useFallback -->
<g id="edge12" class="edge">
<title>hasFallback&#45;&gt;useFallback</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" stroke-dasharray="5,2" d="M567.16,-291.8C570.24,-227.66 576.29,-101.9 578.95,-46.41"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="582.46,-46.45 579.44,-36.29 575.46,-46.11 582.46,-46.45"/>
<text text-anchor="middle" x="583.27" y="-141" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- useFallback&#45;&gt;getEntryFromTable -->
<g id="edge15" class="edge">
<title>useFallback&#45;&gt;getEntryFromTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" stroke-dasharray="5,2" d="M632.83,-36.1C659.89,-48.54 687.27,-68.53 687.27,-98.5 687.27,-558 687.27,-558 687.27,-558 687.27,-578.81 687.73,-589.28 671.27,-602 640.36,-625.89 393.36,-638.69 271.62,-643.6"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="271.15,-640.12 261.3,-644.01 271.43,-647.11 271.15,-640.12"/>
</g>
<!-- loadTable&#45;&gt;getEntryFromTable -->
<g id="edge14" class="edge">
<title>loadTable&#45;&gt;getEntryFromTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" stroke-dasharray="5,2" d="M181.63,-25.84C125.1,-32.94 47.62,-45.46 22.27,-63 7.01,-73.56 0.27,-79.94 0.27,-98.5 0.27,-558 0.27,-558 0.27,-558 0.27,-578.52 -0.81,-588.08 14.27,-602 32.12,-618.47 89.64,-630.3 136.76,-637.52"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="136.54,-641.02 146.94,-639.03 137.57,-634.1 136.54,-641.02"/>
</g>
</g>
</svg>
