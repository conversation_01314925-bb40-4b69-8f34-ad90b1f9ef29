<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.48.0 (20210717.1556)
 -->
<!-- Title: GenderExampleResult Pages: 1 -->
<svg width="313pt" height="74pt"
 viewBox="0.00 0.00 313.00 74.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 70)">
<title>GenderExampleResult</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-70 309,-70 309,4 -4,4"/>
<!-- theSmartString -->
<g id="node1" class="node">
<title>theSmartString</title>
<polygon fill="none" stroke="#daedfd" points="1.5,-47.5 1.5,-64.5 190.5,-64.5 190.5,-47.5 1.5,-47.5"/>
<text text-anchor="start" x="4.5" y="-53.5" font-family="inter" font-size="10.00">{item.gender:choose(Male|Female)El|La}</text>
<polygon fill="none" stroke="#daedfd" points="190.5,-47.5 190.5,-64.5 223.5,-64.5 223.5,-47.5 190.5,-47.5"/>
<text text-anchor="start" x="193.5" y="-53.5" font-family="inter" font-size="10.00">{item}</text>
<polygon fill="none" stroke="#daedfd" points="223.5,-47.5 223.5,-64.5 240.5,-64.5 240.5,-47.5 223.5,-47.5"/>
<text text-anchor="start" x="226.5" y="-53.5" font-family="inter" font-size="10.00">es</text>
<polygon fill="none" stroke="#daedfd" points="240.5,-47.5 240.5,-64.5 304.5,-64.5 304.5,-47.5 240.5,-47.5"/>
<text text-anchor="start" x="243.5" y="-53.5" font-family="inter" font-size="10.00">{item:{color}}</text>
<polygon fill="none" stroke="#daedfd" points="0,-47 0,-66 305,-66 305,-47 0,-47"/>
</g>
<!-- La -->
<g id="node2" class="node">
<title>La</title>
<text text-anchor="middle" x="95.5" y="-3" font-family="inter" font-size="10.00">La</text>
</g>
<!-- La&#45;&gt;theSmartString -->
<g id="edge1" class="edge">
<title>La&#45;&gt;theSmartString:selectorItem1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M95.5,-11.24C95.5,-16.85 95.5,-26.7 95.5,-36.7"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="92,-37 95.5,-47 99,-37 92,-37"/>
</g>
<!-- mochila -->
<g id="node3" class="node">
<title>mochila</title>
<text text-anchor="middle" x="195.5" y="-3" font-family="inter" font-size="10.00">mochila</text>
</g>
<!-- mochila&#45;&gt;theSmartString -->
<g id="edge2" class="edge">
<title>mochila&#45;&gt;theSmartString:selectorItem2</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M197.81,-11.11C200.43,-16.64 204.57,-26.47 206.48,-36.74"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="203.03,-37.4 207.5,-47 209.99,-36.7 203.03,-37.4"/>
</g>
<!-- es -->
<g id="node4" class="node">
<title>es</title>
<text text-anchor="middle" x="236.5" y="-3" font-family="inter" font-size="10.00">es</text>
</g>
<!-- es&#45;&gt;theSmartString -->
<g id="edge3" class="edge">
<title>es&#45;&gt;theSmartString:selectorItem3</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M235.73,-11.22C234.86,-16.88 233.48,-26.86 232.84,-36.99"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="229.34,-36.89 232.5,-47 236.34,-37.12 229.34,-36.89"/>
</g>
<!-- roja -->
<g id="node5" class="node">
<title>roja</title>
<text text-anchor="middle" x="272.5" y="-3" font-family="inter" font-size="10.00">roja</text>
</g>
<!-- roja&#45;&gt;theSmartString -->
<g id="edge4" class="edge">
<title>roja&#45;&gt;theSmartString:selectorItem4</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M272.5,-11.24C272.5,-16.85 272.5,-26.7 272.5,-36.7"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="269,-37 272.5,-47 276,-37 269,-37"/>
</g>
</g>
</svg>
