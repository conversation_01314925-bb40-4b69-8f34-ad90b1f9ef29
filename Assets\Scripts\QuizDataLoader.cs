using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Linq;

/// <summary>
/// <PERSON>les loading quiz data from CSV files with localization support
/// </summary>
public class QuizDataLoader : MonoBehaviour
{
    [Header("Quiz Data Settings")]
    public string csvBaseName = "test"; // Base name without language suffix
    public string csvFolderPath = "QuizQuestions"; // Folder path relative to Assets
    
    [System.Serializable]
    public class QuizQuestion
    {
        public string question;
        public string[] answers;
        public int[] correctAnswers;
        public string category;
        
        public QuizQuestion(string q, string[] ans, int[] correct, string cat)
        {
            question = q;
            answers = ans;
            correctAnswers = correct;
            category = cat;
        }
    }
    
    private static QuizDataLoader instance;
    public static QuizDataLoader Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindFirstObjectByType<QuizDataLoader>();
                if (instance == null)
                {
                    GameObject go = new GameObject("QuizDataLoader");
                    instance = go.AddComponent<QuizDataLoader>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    private Dictionary<string, List<QuizQuestion>> cachedQuizData = new Dictionary<string, List<QuizQuestion>>();
    
    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    /// <summary>
    /// Loads quiz questions for the specified language
    /// </summary>
    /// <param name="languageCode">Language code (e.g., "en", "fr", "es")</param>
    /// <returns>List of quiz questions</returns>
    public List<QuizQuestion> LoadQuizQuestions(string languageCode = "en")
    {
        // Check cache first
        if (cachedQuizData.ContainsKey(languageCode))
        {
            return cachedQuizData[languageCode];
        }
        
        string fileName = $"{csvBaseName}_{languageCode}.csv";
        string filePath = Path.Combine(Application.dataPath, csvFolderPath, fileName);
        
        List<QuizQuestion> questions = new List<QuizQuestion>();
        
        try
        {
            if (File.Exists(filePath))
            {
                questions = ParseCSVFile(filePath);
                cachedQuizData[languageCode] = questions;
                Debug.Log($"Loaded {questions.Count} quiz questions for language: {languageCode}");
            }
            else
            {
                Debug.LogWarning($"Quiz file not found: {filePath}. Falling back to English.");
                // Fallback to English if requested language not found
                if (languageCode != "en")
                {
                    return LoadQuizQuestions("en");
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error loading quiz questions: {e.Message}");
        }
        
        return questions;
    }
    
    /// <summary>
    /// Parses a CSV file and returns quiz questions
    /// </summary>
    /// <param name="filePath">Path to the CSV file</param>
    /// <returns>List of parsed quiz questions</returns>
    private List<QuizQuestion> ParseCSVFile(string filePath)
    {
        List<QuizQuestion> questions = new List<QuizQuestion>();
        string[] lines = File.ReadAllLines(filePath);
        
        // Skip header row
        for (int i = 1; i < lines.Length; i++)
        {
            string line = lines[i];
            if (string.IsNullOrEmpty(line.Trim())) continue;
            
            try
            {
                QuizQuestion question = ParseCSVLine(line);
                if (question != null)
                {
                    questions.Add(question);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Error parsing line {i + 1}: {e.Message}");
            }
        }
        
        return questions;
    }
    
    /// <summary>
    /// Parses a single CSV line into a QuizQuestion
    /// </summary>
    /// <param name="line">CSV line to parse</param>
    /// <returns>Parsed QuizQuestion or null if parsing fails</returns>
    private QuizQuestion ParseCSVLine(string line)
    {
        List<string> fields = ParseCSVFields(line);
        
        if (fields.Count < 8) // Minimum required fields
        {
            Debug.LogWarning($"Invalid CSV line format: {line}");
            return null;
        }
        
        string question = CleanCSVField(fields[0]);
        string[] answers = new string[5];
        
        // Parse answers (fields 1-5)
        for (int i = 0; i < 5; i++)
        {
            answers[i] = CleanCSVField(fields[i + 1]);
        }
        
        // Parse correct answers (field 6)
        string responsesField = CleanCSVField(fields[6]);
        int[] correctAnswers = ParseCorrectAnswers(responsesField);
        
        // Parse category (field 7)
        string category = CleanCSVField(fields[7]);
        
        return new QuizQuestion(question, answers, correctAnswers, category);
    }
    
    /// <summary>
    /// Parses CSV fields handling quoted strings
    /// </summary>
    /// <param name="line">CSV line</param>
    /// <returns>List of parsed fields</returns>
    private List<string> ParseCSVFields(string line)
    {
        List<string> fields = new List<string>();
        bool inQuotes = false;
        string currentField = "";
        
        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];
            
            if (c == '"')
            {
                inQuotes = !inQuotes;
            }
            else if (c == ',' && !inQuotes)
            {
                fields.Add(currentField);
                currentField = "";
            }
            else
            {
                currentField += c;
            }
        }
        
        fields.Add(currentField); // Add the last field
        return fields;
    }
    
    /// <summary>
    /// Cleans a CSV field by removing quotes and trimming
    /// </summary>
    /// <param name="field">Raw CSV field</param>
    /// <returns>Cleaned field</returns>
    private string CleanCSVField(string field)
    {
        return field.Trim().Trim('"');
    }
    
    /// <summary>
    /// Parses the correct answers field (e.g., "[0,3,4]")
    /// </summary>
    /// <param name="responsesField">Responses field from CSV</param>
    /// <returns>Array of correct answer indices</returns>
    private int[] ParseCorrectAnswers(string responsesField)
    {
        try
        {
            // Remove brackets and split by comma
            string cleaned = responsesField.Trim('[', ']');
            string[] parts = cleaned.Split(',');
            
            List<int> correctAnswers = new List<int>();
            foreach (string part in parts)
            {
                if (int.TryParse(part.Trim(), out int index))
                {
                    correctAnswers.Add(index);
                }
            }
            
            return correctAnswers.ToArray();
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"Error parsing correct answers '{responsesField}': {e.Message}");
            return new int[] { 0 }; // Default to first answer
        }
    }
    
    /// <summary>
    /// Gets quiz questions for the current language
    /// </summary>
    /// <returns>List of quiz questions in current language</returns>
    public List<QuizQuestion> GetCurrentLanguageQuestions()
    {
        string currentLanguage = "en";
        if (LocalizationManager.Instance != null)
        {
            currentLanguage = LocalizationManager.Instance.GetCurrentLanguage();
        }
        
        return LoadQuizQuestions(currentLanguage);
    }
    
    /// <summary>
    /// Clears the cache for all languages
    /// </summary>
    public void ClearCache()
    {
        cachedQuizData.Clear();
    }
    
    /// <summary>
    /// Gets available quiz languages based on existing CSV files
    /// </summary>
    /// <returns>List of available language codes</returns>
    public List<string> GetAvailableQuizLanguages()
    {
        List<string> languages = new List<string>();
        string folderPath = Path.Combine(Application.dataPath, csvFolderPath);
        
        if (Directory.Exists(folderPath))
        {
            string[] files = Directory.GetFiles(folderPath, $"{csvBaseName}_*.csv");
            foreach (string file in files)
            {
                string fileName = Path.GetFileNameWithoutExtension(file);
                string languageCode = fileName.Substring(csvBaseName.Length + 1);
                languages.Add(languageCode);
            }
        }
        
        return languages;
    }
}
