<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: JsonSerializer Pages: 1 -->
<svg width="470pt" height="200pt"
 viewBox="0.00 0.00 469.50 200.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="page0,1_graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 196)">
<title>JsonSerializer</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-196 465.5,-196 465.5,4 -4,4"/>
<!-- applyVariant -->
<g id="node1" class="node">
<title>applyVariant</title>
<path fill="#daedfd" stroke="#2196f3" d="M65,-192C65,-192 12,-192 12,-192 6,-192 0,-186 0,-180 0,-180 0,-168 0,-168 0,-162 6,-156 12,-156 12,-156 65,-156 65,-156 71,-156 77,-162 77,-168 77,-168 77,-180 77,-180 77,-186 71,-192 65,-192"/>
<text text-anchor="middle" x="38.5" y="-171.5" font-family="inter" font-size="10.00">Apply Locale</text>
</g>
<!-- updateType -->
<g id="node5" class="node">
<title>updateType</title>
<path fill="#daedfd" stroke="#2196f3" d="M165.94,-188.79C165.94,-188.79 124.19,-177.21 124.19,-177.21 118.41,-175.6 118.41,-172.4 124.19,-170.79 124.19,-170.79 165.94,-159.21 165.94,-159.21 171.72,-157.6 183.28,-157.6 189.06,-159.21 189.06,-159.21 230.81,-170.79 230.81,-170.79 236.59,-172.4 236.59,-175.6 230.81,-177.21 230.81,-177.21 189.06,-188.79 189.06,-188.79 183.28,-190.4 171.72,-190.4 165.94,-188.79"/>
<text text-anchor="middle" x="177.5" y="-171.5" font-family="inter" font-size="10.00">Update Type</text>
</g>
<!-- applyVariant&#45;&gt;updateType -->
<g id="edge1" class="edge">
<title>applyVariant&#45;&gt;updateType</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M77.05,-174C85.42,-174 93.79,-174 102.16,-174"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="102.36,-177.5 112.36,-174 102.36,-170.5 102.36,-177.5"/>
</g>
<!-- readJson -->
<g id="node2" class="node">
<title>readJson</title>
<path fill="#daedfd" stroke="#2196f3" d="M419,-192C419,-192 318,-192 318,-192 312,-192 306,-186 306,-180 306,-180 306,-168 306,-168 306,-162 312,-156 318,-156 318,-156 419,-156 419,-156 425,-156 431,-162 431,-168 431,-168 431,-180 431,-180 431,-186 425,-192 419,-192"/>
<text text-anchor="middle" x="368.5" y="-171.5" font-family="inter" font-size="10.00">Read Object into JSON</text>
</g>
<!-- createProperties -->
<g id="node4" class="node">
<title>createProperties</title>
<path fill="#daedfd" stroke="#2196f3" d="M449.5,-109C449.5,-109 287.5,-109 287.5,-109 281.5,-109 275.5,-103 275.5,-97 275.5,-97 275.5,-85 275.5,-85 275.5,-79 281.5,-73 287.5,-73 287.5,-73 449.5,-73 449.5,-73 455.5,-73 461.5,-79 461.5,-85 461.5,-85 461.5,-97 461.5,-97 461.5,-103 455.5,-109 449.5,-109"/>
<text text-anchor="middle" x="368.5" y="-94" font-family="inter" font-size="10.00">Patch across new values into JSON</text>
<text text-anchor="middle" x="368.5" y="-83" font-family="inter" font-size="10.00">using Localized Variant Values.</text>
</g>
<!-- readJson&#45;&gt;createProperties -->
<g id="edge4" class="edge">
<title>readJson&#45;&gt;createProperties</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M368.5,-155.82C368.5,-145.19 368.5,-131.31 368.5,-119.2"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="372,-119.15 368.5,-109.15 365,-119.15 372,-119.15"/>
</g>
<!-- writeJson -->
<g id="node3" class="node">
<title>writeJson</title>
<path fill="#e9f4e9" stroke="#67bc6b" d="M407.5,-36C407.5,-36 329.5,-36 329.5,-36 323.5,-36 317.5,-30 317.5,-24 317.5,-24 317.5,-12 317.5,-12 317.5,-6 323.5,0 329.5,0 329.5,0 407.5,0 407.5,0 413.5,0 419.5,-6 419.5,-12 419.5,-12 419.5,-24 419.5,-24 419.5,-30 413.5,-36 407.5,-36"/>
<text text-anchor="middle" x="368.5" y="-21" font-family="inter" font-size="10.00">Overwrite Object</text>
<text text-anchor="middle" x="368.5" y="-10" font-family="inter" font-size="10.00">using JSON Patch</text>
</g>
<!-- createProperties&#45;&gt;writeJson -->
<g id="edge5" class="edge">
<title>createProperties&#45;&gt;writeJson</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M368.5,-72.81C368.5,-64.79 368.5,-55.05 368.5,-46.07"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="372,-46.03 368.5,-36.03 365,-46.03 372,-46.03"/>
</g>
<!-- updateType&#45;&gt;readJson -->
<g id="edge2" class="edge">
<title>updateType&#45;&gt;readJson</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M242.43,-174C259.52,-174 278.06,-174 295.54,-174"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="295.69,-177.5 305.69,-174 295.69,-170.5 295.69,-177.5"/>
<text text-anchor="middle" x="274.22" y="-180" font-family="inter" font-size="10.00">Full</text>
</g>
<!-- patchProperties -->
<g id="node6" class="node">
<title>patchProperties</title>
<path fill="#daedfd" stroke="#2196f3" d="M245.5,-109C245.5,-109 109.5,-109 109.5,-109 103.5,-109 97.5,-103 97.5,-97 97.5,-97 97.5,-85 97.5,-85 97.5,-79 103.5,-73 109.5,-73 109.5,-73 245.5,-73 245.5,-73 251.5,-73 257.5,-79 257.5,-85 257.5,-85 257.5,-97 257.5,-97 257.5,-103 251.5,-109 245.5,-109"/>
<text text-anchor="middle" x="177.5" y="-94" font-family="inter" font-size="10.00">Create JSON Properties using</text>
<text text-anchor="middle" x="177.5" y="-83" font-family="inter" font-size="10.00">Localized Variant Values.</text>
</g>
<!-- updateType&#45;&gt;patchProperties -->
<g id="edge3" class="edge">
<title>updateType&#45;&gt;patchProperties</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M177.5,-155.82C177.5,-145.19 177.5,-131.31 177.5,-119.2"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="181,-119.15 177.5,-109.15 174,-119.15 181,-119.15"/>
<text text-anchor="middle" x="193" y="-130" font-family="inter" font-size="10.00">Partial</text>
</g>
<!-- patchProperties&#45;&gt;writeJson -->
<g id="edge6" class="edge">
<title>patchProperties&#45;&gt;writeJson</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M223.25,-72.99C250.22,-62.97 284.53,-50.21 313.04,-39.62"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="314.31,-42.88 322.46,-36.11 311.87,-36.32 314.31,-42.88"/>
</g>
</g>
</svg>
