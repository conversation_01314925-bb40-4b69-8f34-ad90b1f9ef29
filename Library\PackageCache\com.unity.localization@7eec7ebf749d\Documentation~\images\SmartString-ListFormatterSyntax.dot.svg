<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.48.0 (20210717.1556)
 -->
<!-- Title: ListFormatter Pages: 1 -->
<svg width="379pt" height="182pt"
 viewBox="0.00 0.00 378.50 182.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 178)">
<title>ListFormatter</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-178 374.5,-178 374.5,4 -4,4"/>
<!-- smartString -->
<g id="node1" class="node">
<title>smartString</title>
<text text-anchor="start" x="97" y="-84" font-family="inter" font-size="10.00">{</text>
<text text-anchor="start" x="103" y="-84" font-family="inter" font-size="10.00" fill="#f37021">value</text>
<text text-anchor="start" x="130" y="-84" font-family="inter" font-size="10.00">:</text>
<text text-anchor="start" x="135" y="-84" font-family="inter" font-size="10.00" fill="#67bc6b">list</text>
<text text-anchor="start" x="150" y="-84" font-family="inter" font-size="10.00">:</text>
<text text-anchor="start" x="155" y="-84" font-family="inter" font-size="10.00" fill="#765ba7">template</text>
<text text-anchor="start" x="199" y="-84" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="205" y="-84" font-family="inter" font-size="10.00" fill="#765ba7">spacer</text>
<text text-anchor="start" x="238" y="-84" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="244" y="-84" font-family="inter" font-size="10.00" fill="#765ba7">final spacer</text>
<text text-anchor="start" x="298" y="-84" font-family="inter" font-size="10.00">}</text>
</g>
<!-- selector -->
<g id="node2" class="node">
<title>selector</title>
<text text-anchor="middle" x="37" y="-155" font-family="inter" font-size="10.00" fill="#f37021">Any IEnumerator</text>
</g>
<!-- selector&#45;&gt;smartString -->
<g id="edge1" class="edge">
<title>selector&#45;&gt;smartString:selector1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M52.41,-151.9C72.66,-144.91 106.49,-129.63 113.64,-103.94"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="117.12,-104.38 115,-94 110.18,-103.44 117.12,-104.38"/>
</g>
<!-- formatterName -->
<g id="node3" class="node">
<title>formatterName</title>
<text text-anchor="middle" x="141" y="-19.5" font-family="inter" font-size="10.00" fill="#67bc6b">Formatter Name</text>
<text text-anchor="middle" x="141" y="-8.5" font-family="inter" font-size="10.00" fill="#67bc6b">&quot;list&quot; or &quot;l&quot; or implicit</text>
</g>
<!-- formatterName&#45;&gt;smartString -->
<g id="edge2" class="edge">
<title>formatterName&#45;&gt;smartString:name</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M141,-27.51C141,-37.5 141,-53.53 141,-68.99"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="137.5,-69 141,-79 144.5,-69 137.5,-69"/>
</g>
<!-- template -->
<g id="node4" class="node">
<title>template</title>
<text text-anchor="middle" x="176" y="-160.5" font-family="inter" font-size="10.00" fill="#765ba7">Template</text>
<text text-anchor="middle" x="176" y="-149.5" font-family="inter" font-size="10.00" fill="#765ba7">How each item should be formatted.</text>
</g>
<!-- template&#45;&gt;smartString -->
<g id="edge3" class="edge">
<title>template&#45;&gt;smartString:template</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M176,-146.32C176,-136.18 176,-119.88 176,-104.17"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="179.5,-104 176,-94 172.5,-104 179.5,-104"/>
</g>
<!-- spacer -->
<g id="node5" class="node">
<title>spacer</title>
<text text-anchor="middle" x="324" y="-166" font-family="inter" font-size="10.00" fill="#765ba7">Spacer</text>
<text text-anchor="middle" x="324" y="-155" font-family="inter" font-size="10.00" fill="#765ba7">Added after each</text>
<text text-anchor="middle" x="324" y="-144" font-family="inter" font-size="10.00" fill="#765ba7">item except the last</text>
</g>
<!-- spacer&#45;&gt;smartString -->
<g id="edge4" class="edge">
<title>spacer&#45;&gt;smartString:spacer</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M284.77,-140.91C261.34,-130.58 234.33,-116.56 224.67,-103.38"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="227.9,-102.04 221,-94 221.38,-104.59 227.9,-102.04"/>
</g>
<!-- finalSpacer -->
<g id="node6" class="node">
<title>finalSpacer</title>
<text text-anchor="middle" x="270" y="-25" font-family="inter" font-size="10.00" fill="#765ba7">Final Spacer</text>
<text text-anchor="middle" x="270" y="-14" font-family="inter" font-size="10.00" fill="#765ba7">Replaces the last spacer</text>
<text text-anchor="middle" x="270" y="-3" font-family="inter" font-size="10.00" fill="#765ba7">(optional).</text>
</g>
<!-- finalSpacer&#45;&gt;smartString -->
<g id="edge5" class="edge">
<title>finalSpacer&#45;&gt;smartString:finalSpacer</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M270,-33.17C270,-42.98 270,-56.14 270,-68.91"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="266.5,-69 270,-79 273.5,-69 266.5,-69"/>
</g>
</g>
</svg>
