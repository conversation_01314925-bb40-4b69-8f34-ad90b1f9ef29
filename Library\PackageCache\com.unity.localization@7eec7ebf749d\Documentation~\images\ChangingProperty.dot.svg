<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: ChangingProperty Pages: 1 -->
<svg width="393pt" height="594pt"
 viewBox="0.00 0.00 393.00 594.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="page0,1_graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 590)">
<title>ChangingProperty</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-590 389,-590 389,4 -4,4"/>
<!-- changeProp -->
<g id="node1" class="node">
<title>changeProp</title>
<path fill="#daedfd" stroke="#2196f3" d="M240.5,-586C240.5,-586 163.5,-586 163.5,-586 157.5,-586 151.5,-580 151.5,-574 151.5,-574 151.5,-562 151.5,-562 151.5,-556 157.5,-550 163.5,-550 163.5,-550 240.5,-550 240.5,-550 246.5,-550 252.5,-556 252.5,-562 252.5,-562 252.5,-574 252.5,-574 252.5,-580 246.5,-586 240.5,-586"/>
<text text-anchor="middle" x="202" y="-571" font-family="inter" font-size="10.00">Property changed</text>
<text text-anchor="middle" x="202" y="-560" font-family="inter" font-size="10.00">in the Inspector.</text>
</g>
<!-- isTrackChanges -->
<g id="node6" class="node">
<title>isTrackChanges</title>
<path fill="#daedfd" stroke="#2196f3" d="M249.63,-509.17C249.63,-509.17 183.37,-486.83 183.37,-486.83 177.69,-484.92 177.69,-481.08 183.37,-479.17 183.37,-479.17 249.63,-456.83 249.63,-456.83 255.31,-454.92 266.69,-454.92 272.37,-456.83 272.37,-456.83 338.63,-479.17 338.63,-479.17 344.31,-481.08 344.31,-484.92 338.63,-486.83 338.63,-486.83 272.37,-509.17 272.37,-509.17 266.69,-511.08 255.31,-511.08 249.63,-509.17"/>
<text text-anchor="middle" x="261" y="-486" font-family="inter" font-size="10.00">Track Changes</text>
<text text-anchor="middle" x="261" y="-475" font-family="inter" font-size="10.00">Enabled?</text>
</g>
<!-- changeProp&#45;&gt;isTrackChanges -->
<g id="edge1" class="edge">
<title>changeProp&#45;&gt;isTrackChanges</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M214.22,-549.8C221.23,-539.94 230.25,-527.26 238.49,-515.67"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="241.37,-517.65 244.32,-507.47 235.67,-513.59 241.37,-517.65"/>
</g>
<!-- undoPerf -->
<g id="node2" class="node">
<title>undoPerf</title>
<path fill="#daedfd" stroke="#2196f3" d="M359.5,-586C359.5,-586 282.5,-586 282.5,-586 276.5,-586 270.5,-580 270.5,-574 270.5,-574 270.5,-562 270.5,-562 270.5,-556 276.5,-550 282.5,-550 282.5,-550 359.5,-550 359.5,-550 365.5,-550 371.5,-556 371.5,-562 371.5,-562 371.5,-574 371.5,-574 371.5,-580 365.5,-586 359.5,-586"/>
<text text-anchor="middle" x="321" y="-571" font-family="inter" font-size="10.00">Property changed</text>
<text text-anchor="middle" x="321" y="-560" font-family="inter" font-size="10.00">via Undo System.</text>
</g>
<!-- undoPerf&#45;&gt;isTrackChanges -->
<g id="edge2" class="edge">
<title>undoPerf&#45;&gt;isTrackChanges</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M308.57,-549.8C301.44,-539.94 292.27,-527.26 283.89,-515.67"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="286.66,-513.52 277.97,-507.47 280.99,-517.63 286.66,-513.52"/>
</g>
<!-- storeLocally -->
<g id="node3" class="node">
<title>storeLocally</title>
<path fill="#e9f4e9" stroke="#67bc6b" d="M122,-147C122,-147 12,-147 12,-147 6,-147 0,-141 0,-135 0,-135 0,-123 0,-123 0,-117 6,-111 12,-111 12,-111 122,-111 122,-111 128,-111 134,-117 134,-123 134,-123 134,-135 134,-135 134,-141 128,-147 122,-147"/>
<text text-anchor="middle" x="67" y="-132" font-family="inter" font-size="10.00">Save the change directly</text>
<text text-anchor="middle" x="67" y="-121" font-family="inter" font-size="10.00">into the component.</text>
</g>
<!-- storeVariant -->
<g id="node4" class="node">
<title>storeVariant</title>
<path fill="#e9f4e9" stroke="#67bc6b" d="M242.5,-41C242.5,-41 143.5,-41 143.5,-41 137.5,-41 131.5,-35 131.5,-29 131.5,-29 131.5,-12 131.5,-12 131.5,-6 137.5,0 143.5,0 143.5,0 242.5,0 242.5,0 248.5,0 254.5,-6 254.5,-12 254.5,-12 254.5,-29 254.5,-29 254.5,-35 248.5,-41 242.5,-41"/>
<text text-anchor="middle" x="193" y="-29" font-family="inter" font-size="10.00">Save the change as a</text>
<text text-anchor="middle" x="193" y="-18" font-family="inter" font-size="10.00">variant in the</text>
<text text-anchor="middle" x="193" y="-7" font-family="inter" font-size="10.00">GameObject Localizer.</text>
</g>
<!-- storeTable -->
<g id="node5" class="node">
<title>storeTable</title>
<path fill="#e9f4e9" stroke="#67bc6b" d="M373,-38.5C373,-38.5 285,-38.5 285,-38.5 279,-38.5 273,-32.5 273,-26.5 273,-26.5 273,-14.5 273,-14.5 273,-8.5 279,-2.5 285,-2.5 285,-2.5 373,-2.5 373,-2.5 379,-2.5 385,-8.5 385,-14.5 385,-14.5 385,-26.5 385,-26.5 385,-32.5 379,-38.5 373,-38.5"/>
<text text-anchor="middle" x="329" y="-23.5" font-family="inter" font-size="10.00">Save in</text>
<text text-anchor="middle" x="329" y="-12.5" font-family="inter" font-size="10.00">the Localized Table.</text>
</g>
<!-- isTrackChanges&#45;&gt;storeLocally -->
<g id="edge4" class="edge">
<title>isTrackChanges&#45;&gt;storeLocally</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M232.72,-462.25C192.44,-432.28 120,-371.1 89,-299 68.86,-252.15 65.87,-191.79 66.06,-157.34"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="69.56,-157.16 66.22,-147.11 62.56,-157.06 69.56,-157.16"/>
<text text-anchor="middle" x="111" y="-320" font-family="inter" font-size="10.00">No</text>
</g>
<!-- isLocaleActive -->
<g id="node7" class="node">
<title>isLocaleActive</title>
<path fill="#daedfd" stroke="#2196f3" d="M249.84,-401.59C249.84,-401.59 196.16,-380.41 196.16,-380.41 190.58,-378.2 190.58,-373.8 196.16,-371.59 196.16,-371.59 249.84,-350.41 249.84,-350.41 255.42,-348.2 266.58,-348.2 272.16,-350.41 272.16,-350.41 325.84,-371.59 325.84,-371.59 331.42,-373.8 331.42,-378.2 325.84,-380.41 325.84,-380.41 272.16,-401.59 272.16,-401.59 266.58,-403.8 255.42,-403.8 249.84,-401.59"/>
<text text-anchor="middle" x="261" y="-379" font-family="inter" font-size="10.00">Is the Active</text>
<text text-anchor="middle" x="261" y="-368" font-family="inter" font-size="10.00">Locale set?</text>
</g>
<!-- isTrackChanges&#45;&gt;isLocaleActive -->
<g id="edge3" class="edge">
<title>isTrackChanges&#45;&gt;isLocaleActive</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M261,-452.75C261,-441.49 261,-428.43 261,-416.37"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="264.5,-416.09 261,-406.09 257.5,-416.09 264.5,-416.09"/>
<text text-anchor="middle" x="270" y="-427" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- isLocaleActive&#45;&gt;storeLocally -->
<g id="edge5" class="edge">
<title>isLocaleActive&#45;&gt;storeLocally</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M221.97,-361.3C192.46,-349.12 152.9,-328.62 128,-299 109.45,-276.93 85.89,-198.63 74.22,-156.8"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="77.55,-155.72 71.52,-147.01 70.8,-157.58 77.55,-155.72"/>
<text text-anchor="middle" x="135" y="-255.5" font-family="inter" font-size="10.00">No</text>
</g>
<!-- isLocaleProject -->
<g id="node8" class="node">
<title>isLocaleProject</title>
<path fill="#daedfd" stroke="#2196f3" d="M249.76,-294.81C249.76,-294.81 162.24,-262.19 162.24,-262.19 156.62,-260.1 156.62,-255.9 162.24,-253.81 162.24,-253.81 249.76,-221.19 249.76,-221.19 255.38,-219.1 266.62,-219.1 272.24,-221.19 272.24,-221.19 359.76,-253.81 359.76,-253.81 365.38,-255.9 365.38,-260.1 359.76,-262.19 359.76,-262.19 272.24,-294.81 272.24,-294.81 266.62,-296.9 255.38,-296.9 249.76,-294.81"/>
<text text-anchor="middle" x="261" y="-266.5" font-family="inter" font-size="10.00">Is the Active Locale</text>
<text text-anchor="middle" x="261" y="-255.5" font-family="inter" font-size="10.00">the same as the</text>
<text text-anchor="middle" x="261" y="-244.5" font-family="inter" font-size="10.00">Project Locale?</text>
</g>
<!-- isLocaleActive&#45;&gt;isLocaleProject -->
<g id="edge6" class="edge">
<title>isLocaleActive&#45;&gt;isLocaleProject</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M261,-345.73C261,-334.72 261,-321.86 261,-309.46"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="264.5,-309.22 261,-299.22 257.5,-309.22 264.5,-309.22"/>
<text text-anchor="middle" x="270" y="-320" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- isLocaleProject&#45;&gt;storeLocally -->
<g id="edge7" class="edge">
<title>isLocaleProject&#45;&gt;storeLocally</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M221.74,-231.3C186.58,-208.28 135.49,-174.84 101.77,-152.76"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="103.45,-149.68 93.17,-147.13 99.62,-155.54 103.45,-149.68"/>
<text text-anchor="middle" x="179" y="-191" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- isLocalizedProp -->
<g id="node9" class="node">
<title>isLocalizedProp</title>
<path fill="#daedfd" stroke="#2196f3" d="M249.77,-165.78C249.77,-165.78 163.23,-133.22 163.23,-133.22 157.62,-131.11 157.62,-126.89 163.23,-124.78 163.23,-124.78 249.77,-92.22 249.77,-92.22 255.38,-90.11 266.62,-90.11 272.23,-92.22 272.23,-92.22 358.77,-124.78 358.77,-124.78 364.38,-126.89 364.38,-131.11 358.77,-133.22 358.77,-133.22 272.23,-165.78 272.23,-165.78 266.62,-167.89 255.38,-167.89 249.77,-165.78"/>
<text text-anchor="middle" x="261" y="-137.5" font-family="inter" font-size="10.00">Is the property</text>
<text text-anchor="middle" x="261" y="-126.5" font-family="inter" font-size="10.00">a Localized String</text>
<text text-anchor="middle" x="261" y="-115.5" font-family="inter" font-size="10.00">or Localized Asset?</text>
</g>
<!-- isLocaleProject&#45;&gt;isLocalizedProp -->
<g id="edge8" class="edge">
<title>isLocaleProject&#45;&gt;isLocalizedProp</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M261,-216.82C261,-205.35 261,-192.71 261,-180.7"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="264.5,-180.36 261,-170.36 257.5,-180.36 264.5,-180.36"/>
<text text-anchor="middle" x="268" y="-191" font-family="inter" font-size="10.00">No</text>
</g>
<!-- isLocalizedProp&#45;&gt;storeVariant -->
<g id="edge10" class="edge">
<title>isLocalizedProp&#45;&gt;storeVariant</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M240.23,-95.47C230.94,-80.92 220.08,-63.92 211.13,-49.89"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="213.88,-47.7 205.55,-41.15 207.98,-51.47 213.88,-47.7"/>
<text text-anchor="middle" x="230" y="-62" font-family="inter" font-size="10.00">No</text>
</g>
<!-- isLocalizedProp&#45;&gt;storeTable -->
<g id="edge9" class="edge">
<title>isLocalizedProp&#45;&gt;storeTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M281.77,-95.47C291.67,-79.97 303.34,-61.69 312.6,-47.18"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="315.59,-49.01 318.02,-38.7 309.69,-45.24 315.59,-49.01"/>
<text text-anchor="middle" x="312" y="-62" font-family="inter" font-size="10.00">Yes</text>
</g>
</g>
</svg>
