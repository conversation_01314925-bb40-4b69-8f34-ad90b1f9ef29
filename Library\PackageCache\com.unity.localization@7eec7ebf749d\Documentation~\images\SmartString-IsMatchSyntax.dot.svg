<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.48.0 (20210717.1556)
 -->
<!-- Title: IsMatchFormatter Pages: 1 -->
<svg width="295pt" height="138pt"
 viewBox="0.00 0.00 295.00 138.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 134)">
<title>IsMatchFormatter</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-134 291,-134 291,4 -4,4"/>
<!-- smartString -->
<g id="node1" class="node">
<title>smartString</title>
<text text-anchor="start" x="9.5" y="-62" font-family="inter" font-size="10.00">{</text>
<text text-anchor="start" x="15.5" y="-62" font-family="inter" font-size="10.00" fill="#f37021">value</text>
<text text-anchor="start" x="42.5" y="-62" font-family="inter" font-size="10.00">:</text>
<text text-anchor="start" x="47.5" y="-62" font-family="inter" font-size="10.00" fill="#67bc6b">ismatch</text>
<text text-anchor="start" x="84.5" y="-62" font-family="inter" font-size="10.00">(</text>
<text text-anchor="start" x="90.5" y="-62" font-family="inter" font-size="10.00" fill="#eb417a">regex</text>
<text text-anchor="start" x="119.5" y="-62" font-family="inter" font-size="10.00">)</text>
<text text-anchor="start" x="125.5" y="-62" font-family="inter" font-size="10.00" fill="#765ba7">Match output</text>
<text text-anchor="start" x="188.5" y="-62" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="194.5" y="-62" font-family="inter" font-size="10.00" fill="#765ba7">No match output</text>
<text text-anchor="start" x="274.5" y="-62" font-family="inter" font-size="10.00">}</text>
</g>
<!-- selector -->
<g id="node2" class="node">
<title>selector</title>
<text text-anchor="start" x="1" y="-122" font-family="inter" font-size="10.00" fill="#f37021">Any Value</text>
</g>
<!-- selector&#45;&gt;smartString -->
<g id="edge1" class="edge">
<title>selector&#45;&gt;smartString:selector1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M24.09,-118.96C24.99,-111.68 26.68,-96.72 27.28,-82.03"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="30.78,-82.07 27.5,-72 23.78,-81.92 30.78,-82.07"/>
</g>
<!-- formatterName -->
<g id="node3" class="node">
<title>formatterName</title>
<text text-anchor="start" x="26.5" y="-3" font-family="inter" font-size="10.00" fill="#67bc6b">Formatter Name</text>
</g>
<!-- formatterName&#45;&gt;smartString -->
<g id="edge2" class="edge">
<title>formatterName&#45;&gt;smartString:name</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M64.5,-11.23C64.5,-18.43 64.5,-32.78 64.5,-46.88"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="61,-47 64.5,-57 68,-47 61,-47"/>
</g>
<!-- formatterOptions -->
<g id="node4" class="node">
<title>formatterOptions</title>
<text text-anchor="start" x="64.5" y="-122" font-family="inter" font-size="10.00" fill="#eb417a">Regular expression</text>
</g>
<!-- formatterOptions&#45;&gt;smartString -->
<g id="edge3" class="edge">
<title>formatterOptions&#45;&gt;smartString:option1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M106.91,-118.96C106.01,-111.68 104.32,-96.72 103.72,-82.03"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="107.22,-81.92 103.5,-72 100.22,-82.07 107.22,-81.92"/>
</g>
<!-- format -->
<g id="node5" class="node">
<title>format</title>
<text text-anchor="start" x="176" y="-3" font-family="inter" font-size="10.00" fill="#765ba7">Outputs</text>
</g>
<!-- format&#45;&gt;smartString -->
<g id="edge4" class="edge">
<title>format&#45;&gt;smartString:output1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M187.53,-11.18C178.45,-17.81 163.13,-30.88 158.13,-46.78"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="154.62,-46.57 156.5,-57 161.53,-47.68 154.62,-46.57"/>
</g>
<!-- format&#45;&gt;smartString -->
<g id="edge5" class="edge">
<title>format&#45;&gt;smartString:output2</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M201.65,-11.14C210.98,-17.71 226.7,-30.72 231.83,-46.7"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="228.44,-47.69 233.5,-57 235.35,-46.57 228.44,-47.69"/>
</g>
</g>
</svg>
