<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.48.0 (20210717.1556)
 -->
<!-- Title: ChooseFormatter Pages: 1 -->
<svg width="315pt" height="160pt"
 viewBox="0.00 0.00 315.00 160.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 156)">
<title>ChooseFormatter</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-156 311,-156 311,4 -4,4"/>
<!-- smartString -->
<g id="node1" class="node">
<title>smartString</title>
<text text-anchor="start" x="35.5" y="-73" font-family="inter" font-size="10.00">{</text>
<text text-anchor="start" x="41.5" y="-73" font-family="inter" font-size="10.00" fill="#f37021">value</text>
<text text-anchor="start" x="68.5" y="-73" font-family="inter" font-size="10.00">:</text>
<text text-anchor="start" x="73.5" y="-73" font-family="inter" font-size="10.00" fill="#67bc6b">choose</text>
<text text-anchor="start" x="109.5" y="-73" font-family="inter" font-size="10.00">(</text>
<text text-anchor="start" x="115.5" y="-73" font-family="inter" font-size="10.00" fill="#eb417a">1</text>
<text text-anchor="start" x="121.5" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="127.5" y="-73" font-family="inter" font-size="10.00" fill="#eb417a">2</text>
<text text-anchor="start" x="136.5" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="142.5" y="-73" font-family="inter" font-size="10.00" fill="#eb417a">3</text>
<text text-anchor="start" x="151.5" y="-73" font-family="inter" font-size="10.00">):</text>
<text text-anchor="start" x="159.5" y="-73" font-family="inter" font-size="10.00" fill="#765ba7">one</text>
<text text-anchor="start" x="179.5" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="185.5" y="-73" font-family="inter" font-size="10.00" fill="#765ba7">two</text>
<text text-anchor="start" x="205.5" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="211.5" y="-73" font-family="inter" font-size="10.00" fill="#765ba7">three</text>
<text text-anchor="start" x="238.5" y="-73" font-family="inter" font-size="10.00">|</text>
<text text-anchor="start" x="244.5" y="-73" font-family="inter" font-size="10.00">default}</text>
</g>
<!-- selector -->
<g id="node2" class="node">
<title>selector</title>
<text text-anchor="middle" x="22.5" y="-138.5" font-family="inter" font-size="10.00" fill="#f37021">Any Value</text>
</g>
<!-- selector&#45;&gt;smartString -->
<g id="edge1" class="edge">
<title>selector&#45;&gt;smartString:selector1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M27.07,-135.43C34.35,-127.74 48.16,-111.26 52.3,-93.17"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="55.8,-93.34 53.5,-83 48.85,-92.52 55.8,-93.34"/>
</g>
<!-- formatterName -->
<g id="node3" class="node">
<title>formatterName</title>
<text text-anchor="middle" x="78.5" y="-14" font-family="inter" font-size="10.00" fill="#67bc6b">Formatter Name.</text>
<text text-anchor="middle" x="78.5" y="-3" font-family="inter" font-size="10.00" fill="#67bc6b">&quot;choose&quot; or &quot;c&quot;</text>
</g>
<!-- formatterName&#45;&gt;smartString -->
<g id="edge2" class="edge">
<title>formatterName&#45;&gt;smartString:name</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M82.04,-22.18C84.86,-31.04 88.56,-44.52 89.94,-57.88"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="86.46,-58.21 90.5,-68 93.45,-57.82 86.46,-58.21"/>
</g>
<!-- formatterOptions -->
<g id="node4" class="node">
<title>formatterOptions</title>
<text text-anchor="middle" x="131.5" y="-144" font-family="inter" font-size="10.00" fill="#eb417a">Choices</text>
<text text-anchor="middle" x="131.5" y="-133" font-family="inter" font-size="10.00" fill="#eb417a">pipe&#45;separated list of choices</text>
</g>
<!-- formatterOptions&#45;&gt;smartString -->
<g id="edge3" class="edge">
<title>formatterOptions&#45;&gt;smartString:option1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M127.38,-129.68C124.08,-120.69 119.77,-107 118.15,-93.35"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="121.62,-92.76 117.5,-83 114.63,-93.2 121.62,-92.76"/>
</g>
<!-- formatterOptions&#45;&gt;smartString -->
<g id="edge4" class="edge">
<title>formatterOptions&#45;&gt;smartString:option2</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M131.22,-129.91C130.98,-120.83 130.67,-106.87 130.55,-93.25"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="134.05,-92.98 130.5,-83 127.05,-93.02 134.05,-92.98"/>
</g>
<!-- formatterOptions&#45;&gt;smartString -->
<g id="edge5" class="edge">
<title>formatterOptions&#45;&gt;smartString:option3</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M135.62,-129.68C138.92,-120.69 143.23,-107 144.85,-93.35"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="148.37,-93.2 145.5,-83 141.38,-92.76 148.37,-93.2"/>
</g>
<!-- format -->
<g id="node5" class="node">
<title>format</title>
<text text-anchor="middle" x="206.5" y="-14" font-family="inter" font-size="10.00" fill="#765ba7">Outputs.</text>
<text text-anchor="middle" x="206.5" y="-3" font-family="inter" font-size="10.00" fill="#765ba7">(pipe&#45;separated list of outputs)</text>
</g>
<!-- format&#45;&gt;smartString -->
<g id="edge6" class="edge">
<title>format&#45;&gt;smartString:output1</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M194.06,-22.18C185.05,-30.53 173.81,-43.3 169.91,-57.76"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="166.4,-57.62 168.5,-68 173.33,-58.57 166.4,-57.62"/>
</g>
<!-- format&#45;&gt;smartString -->
<g id="edge7" class="edge">
<title>format&#45;&gt;smartString:output2</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M202.96,-22.18C200.14,-31.04 196.44,-44.52 195.06,-57.88"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="191.55,-57.82 194.5,-68 198.54,-58.21 191.55,-57.82"/>
</g>
<!-- format&#45;&gt;smartString -->
<g id="edge8" class="edge">
<title>format&#45;&gt;smartString:output3</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M211.8,-22.01C216.03,-30.75 221.58,-44.13 223.66,-57.68"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="220.2,-58.31 224.5,-68 227.18,-57.75 220.2,-58.31"/>
</g>
<!-- default -->
<g id="node6" class="node">
<title>default</title>
<text text-anchor="middle" x="262.5" y="-144" font-family="inter" font-size="10.00">output if nothing</text>
<text text-anchor="middle" x="262.5" y="-133" font-family="inter" font-size="10.00">matched (optional) </text>
</g>
<!-- default&#45;&gt;smartString -->
<g id="edge9" class="edge">
<title>default&#45;&gt;smartString:output4</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M262.5,-129.91C262.5,-120.83 262.5,-106.86 262.5,-93.25"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="266,-93 262.5,-83 259,-93 266,-93"/>
</g>
</g>
</svg>
