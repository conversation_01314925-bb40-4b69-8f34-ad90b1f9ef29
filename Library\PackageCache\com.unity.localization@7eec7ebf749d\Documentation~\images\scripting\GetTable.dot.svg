<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: GetTable Pages: 1 -->
<svg width="308pt" height="586pt"
 viewBox="0.00 0.00 308.00 586.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="page0,1_graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 582)">
<title>GetTable</title>
<polygon fill="transparent" stroke="transparent" points="-4,4 -4,-582 304,-582 304,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_table</title>
<path fill="#daedfd" stroke="#2196f3" d="M20,-65C20,-65 280,-65 280,-65 286,-65 292,-71 292,-77 292,-77 292,-503 292,-503 292,-509 286,-515 280,-515 280,-515 20,-515 20,-515 14,-515 8,-509 8,-503 8,-503 8,-77 8,-77 8,-71 14,-65 20,-65"/>
<text text-anchor="middle" x="42.5" y="-503" font-family="inter" font-size="10.00">Load Table</text>
</g>
<!-- getTable -->
<g id="node1" class="node">
<title>getTable</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M210.5,-578C210.5,-578 171.5,-578 171.5,-578 165.5,-578 159.5,-572 159.5,-566 159.5,-566 159.5,-554 159.5,-554 159.5,-548 165.5,-542 171.5,-542 171.5,-542 210.5,-542 210.5,-542 216.5,-542 222.5,-548 222.5,-554 222.5,-554 222.5,-566 222.5,-566 222.5,-572 216.5,-578 210.5,-578"/>
<text text-anchor="middle" x="191" y="-557.5" font-family="inter" font-size="10.00">Get Table</text>
</g>
<!-- isTableCached -->
<g id="node2" class="node">
<title>isTableCached</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M237,-488C237,-488 145,-488 145,-488 139,-488 133,-482 133,-476 133,-476 133,-464 133,-464 133,-458 139,-452 145,-452 145,-452 237,-452 237,-452 243,-452 249,-458 249,-464 249,-464 249,-476 249,-476 249,-482 243,-488 237,-488"/>
<text text-anchor="middle" x="191" y="-473" font-family="inter" font-size="10.00">Is the table cached</text>
<text text-anchor="middle" x="191" y="-462" font-family="inter" font-size="10.00">from a previous call?</text>
</g>
<!-- getTable&#45;&gt;isTableCached -->
<g id="edge7" class="edge">
<title>getTable&#45;&gt;isTableCached</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M191,-541.61C191,-529.24 191,-512.37 191,-498.22"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="194.5,-498.05 191,-488.05 187.5,-498.05 194.5,-498.05"/>
</g>
<!-- isTableGuid -->
<g id="node3" class="node">
<title>isTableGuid</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M144.04,-400.12C144.04,-400.12 73.96,-368.88 73.96,-368.88 68.48,-366.44 68.48,-361.56 73.96,-359.12 73.96,-359.12 144.04,-327.88 144.04,-327.88 149.52,-325.44 160.48,-325.44 165.96,-327.88 165.96,-327.88 236.04,-359.12 236.04,-359.12 241.52,-361.56 241.52,-366.44 236.04,-368.88 236.04,-368.88 165.96,-400.12 165.96,-400.12 160.48,-402.56 149.52,-402.56 144.04,-400.12"/>
<text text-anchor="middle" x="155" y="-372.5" font-family="inter" font-size="10.00">Is the</text>
<text text-anchor="middle" x="155" y="-361.5" font-family="inter" font-size="10.00">TableReference</text>
<text text-anchor="middle" x="155" y="-350.5" font-family="inter" font-size="10.00">a GUID?</text>
</g>
<!-- isTableCached&#45;&gt;isTableGuid -->
<g id="edge1" class="edge">
<title>isTableCached&#45;&gt;isTableGuid</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M185.06,-451.83C181.06,-440.28 175.59,-424.49 170.42,-409.54"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="173.66,-408.2 167.08,-399.89 167.04,-410.49 173.66,-408.2"/>
<text text-anchor="middle" x="185" y="-426" font-family="inter" font-size="10.00">No</text>
</g>
<!-- returnTable -->
<g id="node6" class="node">
<title>returnTable</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M247.5,-109C247.5,-109 142.5,-109 142.5,-109 136.5,-109 130.5,-103 130.5,-97 130.5,-97 130.5,-85 130.5,-85 130.5,-79 136.5,-73 142.5,-73 142.5,-73 247.5,-73 247.5,-73 253.5,-73 259.5,-79 259.5,-85 259.5,-85 259.5,-97 259.5,-97 259.5,-103 253.5,-109 247.5,-109"/>
<text text-anchor="middle" x="195" y="-94" font-family="inter" font-size="10.00">Return table handle</text>
<text text-anchor="middle" x="195" y="-83" font-family="inter" font-size="10.00">to async load operation</text>
</g>
<!-- isTableCached&#45;&gt;returnTable -->
<g id="edge6" class="edge">
<title>isTableCached&#45;&gt;returnTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M214.04,-451.8C228.16,-440.1 245.47,-423.43 256,-405 265.09,-389.09 266,-383.32 266,-365 266,-365 266,-365 266,-165.5 266,-144.8 251.25,-127.7 235.2,-115.22"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="236.92,-112.14 226.76,-109.12 232.82,-117.82 236.92,-112.14"/>
<text text-anchor="middle" x="275" y="-247.5" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- requestSharedData -->
<g id="node4" class="node">
<title>requestSharedData</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M168,-276C168,-276 28,-276 28,-276 22,-276 16,-270 16,-264 16,-264 16,-236 16,-236 16,-230 22,-224 28,-224 28,-224 168,-224 168,-224 174,-224 180,-230 180,-236 180,-236 180,-264 180,-264 180,-270 174,-276 168,-276"/>
<text text-anchor="middle" x="98" y="-264" font-family="inter" font-size="10.00">Request the Shared Table</text>
<text text-anchor="middle" x="98" y="-253" font-family="inter" font-size="10.00">Data from Addressables via its</text>
<text text-anchor="middle" x="98" y="-242" font-family="inter" font-size="10.00">GUID and extract the Table</text>
<text text-anchor="middle" x="98" y="-231" font-family="inter" font-size="10.00">Collection Name</text>
</g>
<!-- isTableGuid&#45;&gt;requestSharedData -->
<g id="edge2" class="edge">
<title>isTableGuid&#45;&gt;requestSharedData</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M138.36,-330.3C131.14,-316.11 122.67,-299.48 115.37,-285.13"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="118.42,-283.4 110.76,-276.08 112.18,-286.58 118.42,-283.4"/>
<text text-anchor="middle" x="133" y="-297" font-family="inter" font-size="10.00">Yes</text>
</g>
<!-- requestTable -->
<g id="node5" class="node">
<title>requestTable</title>
<path fill="#f5f5f5" stroke="#2196f3" d="M234.5,-187C234.5,-187 105.5,-187 105.5,-187 99.5,-187 93.5,-181 93.5,-175 93.5,-175 93.5,-158 93.5,-158 93.5,-152 99.5,-146 105.5,-146 105.5,-146 234.5,-146 234.5,-146 240.5,-146 246.5,-152 246.5,-158 246.5,-158 246.5,-175 246.5,-175 246.5,-181 240.5,-187 234.5,-187"/>
<text text-anchor="middle" x="170" y="-175" font-family="inter" font-size="10.00">Request table from</text>
<text text-anchor="middle" x="170" y="-164" font-family="inter" font-size="10.00">Addressables using its Table</text>
<text text-anchor="middle" x="170" y="-153" font-family="inter" font-size="10.00">Collection Name</text>
</g>
<!-- isTableGuid&#45;&gt;requestTable -->
<g id="edge4" class="edge">
<title>isTableGuid&#45;&gt;requestTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M171.26,-329.99C178.21,-314.08 185.52,-294.49 189,-276 193.28,-253.29 192.73,-246.81 189,-224 187.52,-214.97 184.8,-205.44 181.87,-196.83"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="185.09,-195.44 178.39,-187.23 178.51,-197.83 185.09,-195.44"/>
<text text-anchor="middle" x="199" y="-247.5" font-family="inter" font-size="10.00">No</text>
</g>
<!-- requestSharedData&#45;&gt;requestTable -->
<g id="edge3" class="edge">
<title>requestSharedData&#45;&gt;requestTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M120.39,-223.66C128.51,-214.46 137.73,-204.03 145.99,-194.68"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="148.66,-196.94 152.66,-187.13 143.42,-192.31 148.66,-196.94"/>
</g>
<!-- requestTable&#45;&gt;returnTable -->
<g id="edge5" class="edge">
<title>requestTable&#45;&gt;returnTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M176.7,-145.8C179.52,-137.52 182.84,-127.76 185.87,-118.84"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="189.23,-119.84 189.13,-109.25 182.6,-117.59 189.23,-119.84"/>
</g>
<!-- waitForTable -->
<g id="node7" class="node">
<title>waitForTable</title>
<path fill="#e9f4e9" stroke="#67bc6b" d="M248,-36C248,-36 142,-36 142,-36 136,-36 130,-30 130,-24 130,-24 130,-12 130,-12 130,-6 136,0 142,0 142,0 248,0 248,0 254,0 260,-6 260,-12 260,-12 260,-24 260,-24 260,-30 254,-36 248,-36"/>
<text text-anchor="middle" x="195" y="-15.5" font-family="inter" font-size="10.00">Wait for table operation</text>
</g>
<!-- returnTable&#45;&gt;waitForTable -->
<g id="edge8" class="edge">
<title>returnTable&#45;&gt;waitForTable</title>
<path fill="none" stroke="#2196f3" stroke-width="1.5" d="M195,-72.81C195,-64.79 195,-55.05 195,-46.07"/>
<polygon fill="#2196f3" stroke="#2196f3" stroke-width="1.5" points="198.5,-46.03 195,-36.03 191.5,-46.03 198.5,-46.03"/>
</g>
</g>
</svg>
